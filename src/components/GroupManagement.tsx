import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Users, Plus, Edit, Trash2, Search, ArrowLeft } from 'lucide-react'
import GroupDetailsModal from './GroupDetailsModal'

interface Group {
  id: number
  name: string
  permissions_count: number
  users_count: number
}

interface PaginationData {
  current_page: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

interface GroupManagementProps {
  onBack?: () => void
}

export default function GroupManagement({ onBack }: GroupManagementProps) {
  const [groups, setGroups] = useState<Group[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddGroupModal, setShowAddGroupModal] = useState(false)
  const [newGroupName, setNewGroupName] = useState('')
  const [creating, setCreating] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [showGroupDetails, setShowGroupDetails] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    fetchGroups()
  }, [currentPage, searchTerm])

  const fetchGroups = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '10',
        ...(searchTerm && { search: searchTerm }),
      })

      const response = await fetch(`http://localhost:8000/api/admin-controls/groups/?${params}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setGroups(data.data.groups)
          setPagination(data.data.pagination)
        } else {
          setError('Failed to fetch groups')
        }
      } else if (response.status === 403) {
        setError('Permission denied')
      } else {
        setError('Failed to fetch groups')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchGroups()
  }

  const createGroup = async () => {
    if (!newGroupName.trim()) {
      setError('Group name is required')
      return
    }

    setCreating(true)
    try {
      const response = await fetch('http://localhost:8000/api/admin-controls/groups/create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: newGroupName.trim()
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setShowAddGroupModal(false)
          setNewGroupName('')
          fetchGroups() // Refresh the list
        } else {
          setError(data.error || 'Failed to create group')
        }
      } else {
        setError('Failed to create group')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setCreating(false)
    }
  }

  const handleGroupClick = async (group: Group) => {
    setSelectedGroup(group)
    setShowGroupDetails(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading groups...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <button
              onClick={onBack}
              className="p-2 rounded-md hover:bg-muted transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div>
            <h2 className="text-2xl font-bold text-card-foreground flex items-center">
              <Users className="h-6 w-6 mr-2" />
              Groups
            </h2>
            <p className="text-muted-foreground">Manage user groups and permissions</p>
          </div>
        </div>
        <button
          onClick={() => setShowAddGroupModal(true)}
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Group
        </button>
      </div>

      {/* Search */}
      <div className="bg-card rounded-lg border border-border p-4">
        <form onSubmit={handleSearch} className="flex space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder="Search groups..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
          <button
            type="submit"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Search
          </button>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}

      {/* Groups Table */}
      <div className="bg-card rounded-lg border border-border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Group Name</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Permissions</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Users</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {groups.map((group) => (
                <tr key={group.id} className="border-b border-border hover:bg-muted/50">
                  <td className="px-4 py-4">
                    <button
                      onClick={() => handleGroupClick(group)}
                      className="font-medium text-card-foreground hover:text-primary transition-colors text-left"
                    >
                      {group.name}
                    </button>
                  </td>
                  <td className="px-4 py-4">
                    <span className="text-sm text-muted-foreground">{group.permissions_count} permissions</span>
                  </td>
                  <td className="px-4 py-4">
                    <span className="text-sm text-muted-foreground">{group.users_count} users</span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleGroupClick(group)}
                        className="p-1 rounded hover:bg-muted transition-colors"
                        title="View/Edit group"
                      >
                        <Edit className="h-4 w-4 text-muted-foreground" />
                      </button>
                      <button
                        onClick={() => {/* TODO: Implement delete group */}}
                        className="p-1 rounded hover:bg-muted transition-colors"
                        title="Delete group"
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination && pagination.total_pages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 border-t border-border">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.current_page - 1) * 10) + 1} to {Math.min(pagination.current_page * 10, pagination.total_count)} of {pagination.total_count} groups
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={!pagination.has_previous}
                className="px-3 py-1 text-sm border border-input rounded-md hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-sm">
                Page {pagination.current_page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!pagination.has_next}
                className="px-3 py-1 text-sm border border-input rounded-md hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Add Group Modal */}
      {showAddGroupModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg border border-border p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-card-foreground mb-4">Add New Group</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-card-foreground mb-2">
                  Group Name
                </label>
                <input
                  type="text"
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  placeholder="Enter group name"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => {
                  setShowAddGroupModal(false)
                  setNewGroupName('')
                  setError('')
                }}
                className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
                disabled={creating}
              >
                Cancel
              </button>
              <button
                onClick={createGroup}
                disabled={creating || !newGroupName.trim()}
                className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {creating ? 'Creating...' : 'Create Group'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Group Details Modal */}
      <GroupDetailsModal
        group={selectedGroup}
        isOpen={showGroupDetails}
        onClose={() => {
          setShowGroupDetails(false)
          setSelectedGroup(null)
        }}
        onGroupUpdated={fetchGroups}
      />
    </div>
  )
}
