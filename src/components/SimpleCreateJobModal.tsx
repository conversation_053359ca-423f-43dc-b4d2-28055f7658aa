import { useState, useEffect } from 'react'
import { X, Briefcase, Calendar, User, Users, Building, AlertTriangle } from 'lucide-react'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface Vendor {
  id: string
  name: string
  primary_email: string
}

interface SimpleCreateJobModalProps {
  isOpen: boolean
  onClose: () => void
  onJobCreated: () => void
}

export default function SimpleCreateJobModal({ isOpen, onClose, onJobCreated }: SimpleCreateJobModalProps) {
  // Form states
  const [ticketId, setTicketId] = useState('')
  const [jobTitle, setJobTitle] = useState('')
  const [date, setDate] = useState(() => {
    const today = new Date()
    return today.toISOString().split('T')[0] // Format: YYYY-MM-DD
  })
  const [recruiter, setRecruiter] = useState('')
  const [taIncharge, setTaIncharge] = useState('')
  const [client, setClient] = useState('')
  const [interviewPanel, setInterviewPanel] = useState('')
  const [sourcingType, setSourcingType] = useState('')
  const [priority, setPriority] = useState('')
  const [candidatesApplied, setCandidatesApplied] = useState('')
  const [candidatesInterviewed, setCandidatesInterviewed] = useState('')
  const [availability, setAvailability] = useState('')
  const [selectedVendor, setSelectedVendor] = useState('')
  
  const [jobRoles, setJobRoles] = useState<JobRole[]>([])
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen) {
      fetchJobRoles()
      fetchVendors()
      resetForm()
    }
  }, [isOpen])

  const resetForm = () => {
    setTicketId('')
    setJobTitle('')
    const today = new Date()
    setDate(today.toISOString().split('T')[0])
    setRecruiter('')
    setTaIncharge('')
    setClient('')
    setInterviewPanel('')
    setSourcingType('')
    setPriority('')
    setCandidatesApplied('')
    setCandidatesInterviewed('')
    setAvailability('')
    setSelectedVendor('')
    setError('')
  }

  const fetchJobRoles = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/job-management/job-roles/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobRoles(data.data.job_roles)
        }
      }
    } catch (error) {
      console.error('Failed to fetch job roles')
    }
  }

  const fetchVendors = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/vendor-management/vendors/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setVendors(data.data.vendors)
        }
      }
    } catch (error) {
      console.error('Failed to fetch vendors')
    }
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const createJob = async () => {
    if (!jobTitle) {
      setError('Job title is required')
      return
    }

    setCreating(true)
    try {
      const jobData = {
        ticket_id: ticketId.trim() || null,
        job_role_id: jobTitle,
        date: date || null,
        recruiter: recruiter.trim() || null,
        ta_incharge: taIncharge.trim() || null,
        client: client.trim() || null,
        interview_panel: interviewPanel.trim() || null,
        sourcing_type: sourcingType || null,
        priority: priority || null,
        candidates_applied: candidatesApplied ? parseInt(candidatesApplied) : 0,
        candidates_interviewed: candidatesInterviewed ? parseInt(candidatesInterviewed) : 0,
        availability: availability || null,
        vendor_id: sourcingType === 'vendor' ? selectedVendor : null,
      }

      const response = await fetch('http://localhost:8000/api/job-management/jobs/create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(jobData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          handleClose()
          onJobCreated()
        } else {
          setError(data.error || 'Failed to create job')
        }
      } else {
        setError('Failed to create job')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-card-foreground flex items-center">
              <Briefcase className="h-5 w-5 mr-2" />
              Create Job
            </h3>
            <button
              onClick={handleClose}
              className="p-1 rounded hover:bg-muted transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="ticketId" className="block text-sm font-medium mb-1">
                Ticket ID
              </label>
              <input
                id="ticketId"
                type="text"
                value={ticketId}
                onChange={(e) => setTicketId(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="e.g., TH-2025-001"
              />
            </div>

            <div>
              <label htmlFor="jobTitle" className="block text-sm font-medium mb-1">
                Job Title <span className="text-destructive">*</span>
              </label>
              <select
                id="jobTitle"
                value={jobTitle}
                onChange={(e) => setJobTitle(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Select job role</option>
                {jobRoles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.role_name} ({role.years_of_experience} years)
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="date" className="block text-sm font-medium mb-1">
                <Calendar className="h-4 w-4 inline mr-1" />
                Date
              </label>
              <input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>

            <div>
              <label htmlFor="recruiter" className="block text-sm font-medium mb-1">
                <User className="h-4 w-4 inline mr-1" />
                Recruiter
              </label>
              <input
                id="recruiter"
                type="text"
                value={recruiter}
                onChange={(e) => setRecruiter(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="Enter recruiter name"
              />
            </div>

            <div>
              <label htmlFor="taIncharge" className="block text-sm font-medium mb-1">
                <User className="h-4 w-4 inline mr-1" />
                TA Incharge
              </label>
              <input
                id="taIncharge"
                type="text"
                value={taIncharge}
                onChange={(e) => setTaIncharge(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="Enter TA incharge name"
              />
            </div>

            <div>
              <label htmlFor="client" className="block text-sm font-medium mb-1">
                <Building className="h-4 w-4 inline mr-1" />
                Client
              </label>
              <input
                id="client"
                type="text"
                value={client}
                onChange={(e) => setClient(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="Enter client name"
              />
            </div>

            <div>
              <label htmlFor="interviewPanel" className="block text-sm font-medium mb-1">
                <Users className="h-4 w-4 inline mr-1" />
                Interview Panel
              </label>
              <input
                id="interviewPanel"
                type="text"
                value={interviewPanel}
                onChange={(e) => setInterviewPanel(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="Enter interview panel"
              />
            </div>

            <div>
              <label htmlFor="sourcingType" className="block text-sm font-medium mb-1">
                Sourcing Type
              </label>
              <select
                id="sourcingType"
                value={sourcingType}
                onChange={(e) => {
                  setSourcingType(e.target.value)
                  if (e.target.value !== 'vendor') {
                    setSelectedVendor('')
                  }
                }}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Select sourcing type</option>
                <option value="vendor">Vendor</option>
                <option value="direct">Direct</option>
                <option value="internal">Internal</option>
                <option value="all">All</option>
              </select>
            </div>

            {/* Vendor Selection - Show only when sourcing type is vendor */}
            {sourcingType === 'vendor' && (
              <div>
                <label htmlFor="selectedVendor" className="block text-sm font-medium mb-1">
                  Select Vendor
                </label>
                <select
                  id="selectedVendor"
                  value={selectedVendor}
                  onChange={(e) => setSelectedVendor(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="">Select a vendor</option>
                  {vendors.map((vendor) => (
                    <option key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div>
              <label htmlFor="priority" className="block text-sm font-medium mb-1">
                <AlertTriangle className="h-4 w-4 inline mr-1" />
                Priority
              </label>
              <select
                id="priority"
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Select priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            <div>
              <label htmlFor="candidatesApplied" className="block text-sm font-medium mb-1">
                Candidates Applied
              </label>
              <input
                id="candidatesApplied"
                type="number"
                min="0"
                value={candidatesApplied}
                onChange={(e) => setCandidatesApplied(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="0"
              />
            </div>

            <div>
              <label htmlFor="candidatesInterviewed" className="block text-sm font-medium mb-1">
                Candidates Interviewed
              </label>
              <input
                id="candidatesInterviewed"
                type="number"
                min="0"
                value={candidatesInterviewed}
                onChange={(e) => setCandidatesInterviewed(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="0"
              />
            </div>

            <div>
              <label htmlFor="availability" className="block text-sm font-medium mb-1">
                Availability
              </label>
              <select
                id="availability"
                value={availability}
                onChange={(e) => setAvailability(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">Select availability</option>
                <option value="vacant">Vacant</option>
                <option value="hired">Hired</option>
              </select>
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-border flex justify-end space-x-2">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
            disabled={creating}
          >
            Cancel
          </button>
          <button
            onClick={createJob}
            disabled={creating || !jobTitle}
            className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? 'Creating...' : 'Create Job'}
          </button>
        </div>
      </div>
    </div>
  )
}
