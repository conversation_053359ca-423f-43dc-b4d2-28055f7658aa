import { useState, useEffect } from 'react'
import { X, Building, Users, Plus, Edit } from 'lucide-react'

interface User {
  id: string
  email: string
  full_name: string
  user_type: string
  is_active: boolean
  department?: string
  position?: string
  phone_number?: string
}

interface Vendor {
  id: string
  name: string
  primary_email: string
  primary_contact: string
  website: string
  description: string
  users: string[]
  user_details?: User[]
}

interface VendorDetailsModalProps {
  vendor: Vendor | null
  isOpen: boolean
  onClose: () => void
  onVendorUpdated: () => void
}

export default function VendorDetailsModal({ vendor, isOpen, onClose, onVendorUpdated }: VendorDetailsModalProps) {
  const [vendorDetails, setVendorDetails] = useState<Vendor | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [name, setName] = useState('')
  const [primaryEmail, setPrimaryEmail] = useState('')
  const [primaryContact, setPrimaryContact] = useState('')
  const [website, setWebsite] = useState('')
  const [description, setDescription] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [showEditUsers, setShowEditUsers] = useState(false)
  const [editing, setEditing] = useState(false)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (isOpen && vendor) {
      fetchVendorDetails()
    }
  }, [isOpen, vendor])

  useEffect(() => {
    if (vendorDetails) {
      setName(vendorDetails.name)
      setPrimaryEmail(vendorDetails.primary_email)
      setPrimaryContact(vendorDetails.primary_contact || '')
      setWebsite(vendorDetails.website || '')
      setDescription(vendorDetails.description || '')
      setSelectedUsers(vendorDetails.users || [])
    }
  }, [vendorDetails])

  const fetchAvailableUsers = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/vendor-management/vendor-users/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAvailableUsers(data.data)
        }
      }
    } catch (error) {
      console.error('Failed to fetch available users')
    }
  }

  const handleUserSelection = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId))
    } else {
      setSelectedUsers([...selectedUsers, userId])
    }
  }

  const fetchVendorDetails = async () => {
    if (!vendor) return

    setLoading(true)
    try {
      const response = await fetch(`http://localhost:8000/api/vendor-management/vendors/${vendor.id}/`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setVendorDetails(data.data)
        } else {
          setError('Failed to fetch vendor details')
        }
      } else {
        setError('Failed to fetch vendor details')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const saveVendor = async () => {
    if (!vendor) return

    setSaving(true)
    try {
      const response = await fetch(`http://localhost:8000/api/vendor-management/vendors/${vendor.id}/update/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name,
          primary_email: primaryEmail,
          primary_contact: primaryContact,
          website,
          description,
          users: selectedUsers,
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setEditing(false)
          fetchVendorDetails()
          onVendorUpdated()
        } else {
          setError(data.error || 'Failed to update vendor')
        }
      } else {
        setError('Failed to update vendor')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setSaving(false)
    }
  }

  if (!isOpen || !vendor) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-card-foreground flex items-center">
              <Building className="h-5 w-5 mr-2" />
              {editing ? 'Edit Vendor' : vendorDetails?.name}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-muted transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading vendor details...</p>
          </div>
        ) : (
          <>
            {editing ? (
              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-1">
                    Vendor Name <span className="text-destructive">*</span>
                  </label>
                  <input
                    id="name"
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>

                <div>
                  <label htmlFor="primaryEmail" className="block text-sm font-medium mb-1">
                    Primary Email <span className="text-destructive">*</span>
                  </label>
                  <input
                    id="primaryEmail"
                    type="email"
                    value={primaryEmail}
                    onChange={(e) => setPrimaryEmail(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>

                <div>
                  <label htmlFor="primaryContact" className="block text-sm font-medium mb-1">
                    Primary Contact Number
                  </label>
                  <input
                    id="primaryContact"
                    type="text"
                    value={primaryContact}
                    onChange={(e) => setPrimaryContact(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>

                <div>
                  <label htmlFor="website" className="block text-sm font-medium mb-1">
                    Website (Optional)
                  </label>
                  <input
                    id="website"
                    type="text"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    rows={3}
                  />
                </div>

                <div className="mt-6 flex justify-end space-x-2">
                  <button
                    onClick={() => setEditing(false)}
                    className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
                    disabled={saving}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={saveVendor}
                    disabled={saving || !name.trim() || !primaryEmail.trim()}
                    className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="flex justify-end mb-4">
                  <button
                    onClick={() => setEditing(true)}
                    className="px-3 py-1 text-sm bg-muted hover:bg-muted/80 rounded-md transition-colors flex items-center"
                  >
                    <Edit className="h-3.5 w-3.5 mr-1" />
                    Edit Vendor
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Primary Email</h4>
                    <p className="text-card-foreground">{vendorDetails?.primary_email}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Primary Contact</h4>
                    <p className="text-card-foreground">{vendorDetails?.primary_contact || 'N/A'}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Website</h4>
                    <p className="text-card-foreground">
                      {vendorDetails?.website ? (
                        <a href={vendorDetails.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                          {vendorDetails.website}
                        </a>
                      ) : 'N/A'}
                    </p>
                  </div>
                </div>

                {vendorDetails?.description && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Description</h4>
                    <p className="text-card-foreground whitespace-pre-wrap">{vendorDetails.description}</p>
                  </div>
                )}

                <div className="border-t border-border pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-base font-medium flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      Associated Users
                    </h4>
                    <button
                      onClick={() => {
                        setShowEditUsers(true)
                        fetchAvailableUsers()
                      }}
                      className="px-3 py-1 text-sm bg-muted hover:bg-muted/80 rounded-md transition-colors flex items-center"
                    >
                      <Edit className="h-3.5 w-3.5 mr-1" />
                      Edit Users
                    </button>
                  </div>

                  {showEditUsers ? (
                    <div className="mb-4">
                      <div className="max-h-40 overflow-y-auto border border-input rounded-md p-2 mb-3">
                        {availableUsers.length === 0 ? (
                          <div className="text-sm text-muted-foreground p-2">Loading users...</div>
                        ) : (
                          availableUsers.map((user) => (
                            <div key={user.id} className="flex items-center py-1">
                              <input
                                type="checkbox"
                                id={`user-${user.id}`}
                                checked={selectedUsers.includes(user.id)}
                                onChange={() => handleUserSelection(user.id)}
                                className="mr-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                              />
                              <label htmlFor={`user-${user.id}`} className="text-sm">
                                {user.full_name} ({user.email})
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => {
                            setShowEditUsers(false)
                            setSelectedUsers(vendorDetails?.users || [])
                          }}
                          className="px-3 py-1 text-sm border border-input rounded-md hover:bg-muted transition-colors"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={saveVendor}
                          className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                        >
                          Save Changes
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="max-h-[30vh] overflow-y-auto border border-border rounded-md">
                      {vendorDetails?.user_details?.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground">
                          No users associated with this vendor
                        </div>
                      ) : (
                        <div className="divide-y divide-border">
                          {vendorDetails?.user_details?.map((user) => (
                            <div key={user.id} className="p-3 hover:bg-muted/50">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h5 className="font-medium">{user.full_name}</h5>
                                  <p className="text-sm text-muted-foreground">{user.email}</p>
                                  {user.department && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {user.department} • {user.position}
                                    </p>
                                  )}
                                </div>
                                <div className="text-xs px-2 py-1 rounded-full bg-muted">
                                  {user.is_active ? 'Active' : 'Inactive'}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="border-t border-border mt-6 pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-base font-medium">Job Requirements</h4>
                    <button className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center">
                      <Plus className="h-3.5 w-3.5 mr-1" />
                      Add Requirement
                    </button>
                  </div>
                  <div className="p-4 text-center text-muted-foreground border border-border rounded-md">
                    No job requirements shared with this vendor yet
                  </div>
                </div>
              </>
            )}
          </>
        )}
      </div>
    </div>
  )
}

// Edit icon component
function Edit(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
    </svg>
  )
}
