import { useState, useEffect } from 'react'
import { X, User<PERSON>he<PERSON>, Upload, ChevronDown, ChevronRight } from 'lucide-react'
import SimpleMultipleSelector, { Option } from './SimpleMultipleSelector'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface InterviewRound {
  round_name: string
  status: string
  schedule_date: string
  schedule_time: string
  panel_name: string
  panel_comment_score: string
}

interface SimpleCreateCandidateModalProps {
  isOpen: boolean
  onClose: () => void
  onCandidateCreated: () => void
}

export default function SimpleCreateCandidateModal({ isOpen, onClose, onCandidateCreated }: SimpleCreateCandidateModalProps) {
  // Form states
  const [fullName, setFullName] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [email, setEmail] = useState('')
  const [candidateId, setCandidateId] = useState('')
  const [preferredRole, setPreferredRole] = useState('')
  const [optionalRoles, setOptionalRoles] = useState<Option[]>([])
  const [totalExperience, setTotalExperience] = useState('')
  const [lastJobDate, setLastJobDate] = useState('')
  const [resumeFile, setResumeFile] = useState<File | null>(null)
  const [comments, setComments] = useState('')
  
  // Interview rounds state
  const [interviewRounds, setInterviewRounds] = useState<InterviewRound[]>([
    {
      round_name: 'L1 Round',
      status: '',
      schedule_date: '',
      schedule_time: '',
      panel_name: '',
      panel_comment_score: ''
    }
  ])
  
  // Bulk upload states
  const [bulkFile, setBulkFile] = useState<File | null>(null)
  
  // Other states
  const [jobRoles, setJobRoles] = useState<JobRole[]>([])
  const [expandedRounds, setExpandedRounds] = useState<Set<string>>(new Set())
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen) {
      fetchJobRoles()
      resetForm()
    }
  }, [isOpen])

  const resetForm = () => {
    setFullName('')
    setPhoneNumber('')
    setEmail('')
    setCandidateId('')
    setPreferredRole('')
    setOptionalRoles([])
    setTotalExperience('')
    setLastJobDate('')
    setResumeFile(null)
    setComments('')
    setInterviewRounds([{
      round_name: 'L1 Round',
      status: '',
      schedule_date: '',
      schedule_time: '',
      panel_name: '',
      panel_comment_score: ''
    }])
    setBulkFile(null)
    setExpandedRounds(new Set())
    setError('')
  }

  const fetchJobRoles = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/job-management/job-roles/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobRoles(data.data.job_roles)
        }
      }
    } catch (error) {
      console.error('Failed to fetch job roles')
    }
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const getAvailableOptionalRoles = (): Option[] => {
    return jobRoles
      .filter(role => role.id !== preferredRole)
      .map(role => ({
        label: `${role.role_name} (${role.years_of_experience} years)`,
        value: role.id
      }))
  }

  const handleResumeFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a PDF or DOC file for resume')
        return
      }
      
      if (file.size > 10 * 1024 * 1024) {
        setError('Resume file size must be less than 10MB')
        return
      }
      
      setResumeFile(file)
      setError('')
    }
  }

  const handleBulkFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select an Excel or CSV file')
        return
      }
      
      setBulkFile(file)
      setError('')
    }
  }

  const updateInterviewRound = (index: number, field: keyof InterviewRound, value: string) => {
    const updatedRounds = [...interviewRounds]
    if (!updatedRounds[index]) {
      updatedRounds[index] = {
        round_name: `L${index + 1} Round`,
        status: '',
        schedule_date: '',
        schedule_time: '',
        panel_name: '',
        panel_comment_score: ''
      }
    }
    updatedRounds[index] = { ...updatedRounds[index], [field]: value }
    setInterviewRounds(updatedRounds)
  }

  const toggleRoundExpansion = (roundName: string) => {
    const newExpanded = new Set(expandedRounds)
    if (newExpanded.has(roundName)) {
      newExpanded.delete(roundName)
    } else {
      newExpanded.add(roundName)
    }
    setExpandedRounds(newExpanded)
  }

  const createCandidate = async () => {
    if (!fullName.trim()) {
      setError('Full name is required')
      return
    }

    setCreating(true)
    try {
      const candidateData = {
        full_name: fullName.trim(),
        phone_number: phoneNumber.trim(),
        email: email.trim(),
        candidate_id: candidateId.trim(),
        preferred_role: preferredRole,
        optional_roles: optionalRoles.map(role => role.value),
        total_experience: totalExperience.trim(),
        last_job_date: lastJobDate || null,
        comments: comments.trim(),
        interview_rounds: interviewRounds.filter(round => round.status || round.schedule_date || round.panel_name)
      }

      // TODO: Implement candidate creation API endpoint
      console.log('Candidate data to be sent:', candidateData)
      
      // Simulate success for now
      setTimeout(() => {
        handleClose()
        onCandidateCreated()
        setCreating(false)
      }, 1000)

    } catch (error) {
      setError('Network error')
      setCreating(false)
    }
  }

  const handleBulkUpload = async () => {
    if (!bulkFile) {
      setError('Please select a file for bulk upload')
      return
    }

    setCreating(true)
    try {
      // TODO: Implement bulk upload functionality
      console.log('Bulk file to be uploaded:', bulkFile.name)
      
      // Simulate success for now
      setTimeout(() => {
        handleClose()
        onCandidateCreated()
        setCreating(false)
      }, 1000)

    } catch (error) {
      setError('Network error')
      setCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border w-full max-w-7xl max-h-[95vh] flex">
        {/* Left Panel - Bulk Upload */}
        <div className="w-1/3 border-r border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center">
              <Upload className="h-5 w-5 mr-2" />
              Bulk Upload
            </h3>
            <p className="text-sm text-muted-foreground mt-1">Upload candidates using Excel or CSV file</p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Upload File
                </label>
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleBulkFileChange}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Supported formats: Excel (.xlsx, .xls) or CSV (.csv)
                </p>
                {bulkFile && (
                  <div className="mt-2 p-2 bg-muted rounded-md">
                    <p className="text-sm text-card-foreground">
                      Selected: {bulkFile.name}
                    </p>
                  </div>
                )}
              </div>

              <div className="bg-muted/50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-card-foreground mb-2">File Format Requirements:</h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Full Name (Required)</li>
                  <li>• Phone Number</li>
                  <li>• Email</li>
                  <li>• Candidate ID</li>
                  <li>• Preferred Role</li>
                  <li>• Total Experience</li>
                  <li>• Comments</li>
                </ul>
              </div>

              <button
                onClick={handleBulkUpload}
                disabled={creating || !bulkFile}
                className="w-full px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {creating ? 'Uploading...' : 'Upload Candidates'}
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Form */}
        <div className="w-2/3 flex flex-col">
          <div className="p-4 border-b border-border flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-card-foreground flex items-center">
                <UserCheck className="h-5 w-5 mr-2" />
                Create Candidate
              </h3>
              <p className="text-sm text-muted-foreground mt-1">Fill in candidate details manually</p>
            </div>
            <button
              onClick={handleClose}
              className="p-1 rounded hover:bg-muted transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {error && (
              <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
                {error}
              </div>
            )}

            <div className="space-y-4">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium mb-1">
                    Full Name <span className="text-destructive">*</span>
                  </label>
                  <input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter full name"
                  />
                </div>

                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium mb-1">
                    Phone Number
                  </label>
                  <input
                    id="phoneNumber"
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-1">
                    Email
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter email address"
                  />
                </div>

                <div>
                  <label htmlFor="candidateId" className="block text-sm font-medium mb-1">
                    Candidate ID
                  </label>
                  <input
                    id="candidateId"
                    type="text"
                    value={candidateId}
                    onChange={(e) => setCandidateId(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter candidate ID"
                  />
                </div>
              </div>

              {/* Role Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="preferredRole" className="block text-sm font-medium mb-1">
                    Preferred Role
                  </label>
                  <select
                    id="preferredRole"
                    value={preferredRole}
                    onChange={(e) => {
                      setPreferredRole(e.target.value)
                      // Remove from optional roles if selected as preferred
                      setOptionalRoles(prev => prev.filter(role => role.value !== e.target.value))
                    }}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  >
                    <option value="">Select preferred role</option>
                    {jobRoles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.role_name} ({role.years_of_experience} years)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Optional Roles
                  </label>
                  <SimpleMultipleSelector
                    value={optionalRoles}
                    onChange={setOptionalRoles}
                    options={getAvailableOptionalRoles()}
                    placeholder="Select optional roles..."
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="totalExperience" className="block text-sm font-medium mb-1">
                    Total Experience
                  </label>
                  <input
                    id="totalExperience"
                    type="text"
                    value={totalExperience}
                    onChange={(e) => setTotalExperience(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="e.g., 3 years 6 months"
                  />
                </div>

                <div>
                  <label htmlFor="lastJobDate" className="block text-sm font-medium mb-1">
                    Last Job Date
                  </label>
                  <input
                    id="lastJobDate"
                    type="date"
                    value={lastJobDate}
                    onChange={(e) => setLastJobDate(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Resume Upload
                </label>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleResumeFileChange}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Supported formats: PDF, DOC, DOCX (max 10MB)
                </p>
                {resumeFile && (
                  <div className="mt-2 p-2 bg-muted rounded-md">
                    <p className="text-sm text-card-foreground">
                      Selected: {resumeFile.name} ({(resumeFile.size / 1024 / 1024).toFixed(2)} MB)
                    </p>
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="comments" className="block text-sm font-medium mb-1">
                  Comments
                </label>
                <textarea
                  id="comments"
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="Additional comments about the candidate"
                  rows={3}
                />
              </div>

              {/* Interview Rounds Section */}
              <div className="border-t border-border pt-4">
                <h4 className="text-lg font-semibold text-card-foreground mb-4">Interview Rounds</h4>
                
                {/* L1 Round - Always visible */}
                <div className="mb-4">
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h5 className="font-medium text-card-foreground mb-3">L1 Round</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium mb-1">Status</label>
                        <select
                          value={interviewRounds[0]?.status || ''}
                          onChange={(e) => updateInterviewRound(0, 'status', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                        >
                          <option value="">Select status</option>
                          <option value="scheduled">Scheduled</option>
                          <option value="attended">Attended</option>
                          <option value="hold">Hold</option>
                          <option value="rejected">Rejected</option>
                          <option value="selected">Selected</option>
                          <option value="dropped-out">Dropped-Out</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Schedule Date</label>
                        <input
                          type="date"
                          value={interviewRounds[0]?.schedule_date || ''}
                          onChange={(e) => updateInterviewRound(0, 'schedule_date', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Schedule Time</label>
                        <input
                          type="time"
                          value={interviewRounds[0]?.schedule_time || ''}
                          onChange={(e) => updateInterviewRound(0, 'schedule_time', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Panel Name</label>
                        <input
                          type="text"
                          value={interviewRounds[0]?.panel_name || ''}
                          onChange={(e) => updateInterviewRound(0, 'panel_name', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          placeholder="Enter panel name"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium mb-1">Panel Comment/Score</label>
                        <textarea
                          value={interviewRounds[0]?.panel_comment_score || ''}
                          onChange={(e) => updateInterviewRound(0, 'panel_comment_score', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          placeholder="Enter comments or score"
                          rows={2}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* L2-L6 Rounds - Collapsible */}
                {['L2 Round', 'L3 Round', 'L4 Round', 'L5 Round', 'L6 Round'].map((roundName, index) => {
                  const roundIndex = index + 1
                  const isExpanded = expandedRounds.has(roundName)

                  return (
                    <div key={roundName} className="mb-2">
                      <button
                        type="button"
                        onClick={() => toggleRoundExpansion(roundName)}
                        className="w-full flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <span className="font-medium text-card-foreground">{roundName}</span>
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </button>
                      
                      {isExpanded && (
                        <div className="mt-2 bg-muted/50 rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                              <label className="block text-sm font-medium mb-1">Status</label>
                              <select
                                value={interviewRounds[roundIndex]?.status || ''}
                                onChange={(e) => updateInterviewRound(roundIndex, 'status', e.target.value)}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                              >
                                <option value="">Select status</option>
                                <option value="scheduled">Scheduled</option>
                                <option value="attended">Attended</option>
                                <option value="hold">Hold</option>
                                <option value="rejected">Rejected</option>
                                <option value="selected">Selected</option>
                                <option value="dropped-out">Dropped-Out</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-1">Schedule Date</label>
                              <input
                                type="date"
                                value={interviewRounds[roundIndex]?.schedule_date || ''}
                                onChange={(e) => updateInterviewRound(roundIndex, 'schedule_date', e.target.value)}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-1">Schedule Time</label>
                              <input
                                type="time"
                                value={interviewRounds[roundIndex]?.schedule_time || ''}
                                onChange={(e) => updateInterviewRound(roundIndex, 'schedule_time', e.target.value)}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-1">Panel Name</label>
                              <input
                                type="text"
                                value={interviewRounds[roundIndex]?.panel_name || ''}
                                onChange={(e) => updateInterviewRound(roundIndex, 'panel_name', e.target.value)}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                                placeholder="Enter panel name"
                              />
                            </div>
                            <div className="md:col-span-2">
                              <label className="block text-sm font-medium mb-1">Panel Comment/Score</label>
                              <textarea
                                value={interviewRounds[roundIndex]?.panel_comment_score || ''}
                                onChange={(e) => updateInterviewRound(roundIndex, 'panel_comment_score', e.target.value)}
                                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                                placeholder="Enter comments or score"
                                rows={2}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-border flex justify-end space-x-2">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
              disabled={creating}
            >
              Cancel
            </button>
            <button
              onClick={createCandidate}
              disabled={creating || !fullName.trim()}
              className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {creating ? 'Creating...' : 'Create Candidate'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
