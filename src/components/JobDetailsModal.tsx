import { useState, useEffect } from 'react'
import { X, Briefcase, Calendar, User, Users, Building, AlertTriangle, Plus } from 'lucide-react'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface Job {
  id: string
  ticket_id: string
  job_title: string
  job_role_id: string
  job_role?: JobRole
  date: string
  recruiter: string
  ta_incharge: string
  client: string
  interview_panel: string
  sourcing_type: string
  priority: string
  candidates_applied: number
  candidates_interviewed: number
  availability: string
  vendor_id: string
  is_active: boolean
  is_published: boolean
  created_at: string
}

interface JobDetailsModalProps {
  job: Job | null
  isOpen: boolean
  onClose: () => void
}

export default function JobDetailsModal({ job, isOpen, onClose }: JobDetailsModalProps) {
  if (!isOpen || !job) return null

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-100'
      case 'medium': return 'text-yellow-500 bg-yellow-100'
      case 'low': return 'text-green-500 bg-green-100'
      default: return 'text-gray-500 bg-gray-100'
    }
  }

  const getSourcingTypeColor = (type: string) => {
    switch (type) {
      case 'vendor': return 'bg-blue-100 text-blue-800'
      case 'direct': return 'bg-green-100 text-green-800'
      case 'internal': return 'bg-purple-100 text-purple-800'
      case 'all': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'vacant': return 'bg-green-100 text-green-800'
      case 'hired': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-semibold text-card-foreground flex items-center">
                <Briefcase className="h-5 w-5 mr-2" />
                Job Selection - {job.job_title}
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                {job.ticket_id && `Ticket: ${job.ticket_id} • `}
                {job.job_role && `${job.job_role.role_name} (${job.job_role.years_of_experience} years exp.)`}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-1 rounded hover:bg-muted transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Job Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Date</span>
              </div>
              <p className="text-card-foreground">{job.date || 'Not specified'}</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Recruiter</span>
              </div>
              <p className="text-card-foreground">{job.recruiter || 'Not assigned'}</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <User className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">TA Incharge</span>
              </div>
              <p className="text-card-foreground">{job.ta_incharge || 'Not assigned'}</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Client</span>
              </div>
              <p className="text-card-foreground">{job.client || 'Not specified'}</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Interview Panel</span>
              </div>
              <p className="text-card-foreground">{job.interview_panel || 'Not specified'}</p>
            </div>

            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <AlertTriangle className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Priority</span>
              </div>
              {job.priority ? (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(job.priority)}`}>
                  {job.priority.charAt(0).toUpperCase() + job.priority.slice(1)}
                </span>
              ) : (
                <p className="text-muted-foreground">Not specified</p>
              )}
            </div>
          </div>

          {/* Statistics Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-card-foreground">{job.candidates_applied}</div>
              <div className="text-sm text-muted-foreground">Candidates Applied</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-card-foreground">{job.candidates_interviewed}</div>
              <div className="text-sm text-muted-foreground">Candidates Interviewed</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              {job.sourcing_type ? (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getSourcingTypeColor(job.sourcing_type)}`}>
                  {job.sourcing_type.charAt(0).toUpperCase() + job.sourcing_type.slice(1)}
                </span>
              ) : (
                <span className="text-muted-foreground">Not specified</span>
              )}
              <div className="text-sm text-muted-foreground mt-1">Sourcing Type</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              {job.availability ? (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getAvailabilityColor(job.availability)}`}>
                  {job.availability.charAt(0).toUpperCase() + job.availability.slice(1)}
                </span>
              ) : (
                <span className="text-muted-foreground">Not specified</span>
              )}
              <div className="text-sm text-muted-foreground mt-1">Availability</div>
            </div>
          </div>

          {/* Candidates Section */}
          <div className="border-t border-border pt-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-card-foreground">Candidates</h4>
              <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2">
                <Plus className="h-4 w-4 mr-2" />
                Add Candidate
              </button>
            </div>
            
            <div className="bg-muted/30 rounded-lg p-8 text-center">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h5 className="text-lg font-medium text-card-foreground mb-2">No candidates added yet</h5>
              <p className="text-muted-foreground mb-4">
                Start building your candidate pipeline by adding candidates to this job.
              </p>
              <button className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2">
                <Plus className="h-4 w-4 mr-2" />
                Add First Candidate
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
