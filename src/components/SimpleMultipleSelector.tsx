import * as React from "react"
import { X } from "lucide-react"

export interface Option {
  label: string
  value: string
  disabled?: boolean
}

interface SimpleMultipleSelectorProps {
  value?: Option[]
  onChange?: (value: Option[]) => void
  options?: Option[]
  placeholder?: string
  className?: string
  disabled?: boolean
}

export default function SimpleMultipleSelector({
  value = [],
  onChange,
  options = [],
  placeholder = "Select options...",
  className = "",
  disabled = false,
}: SimpleMultipleSelectorProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const inputRef = React.useRef<HTMLInputElement>(null)

  const availableOptions = React.useMemo(() => {
    return options.filter(option => 
      !value.some(selected => selected.value === option.value) &&
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, value, searchValue])

  const handleSelect = (option: Option) => {
    if (disabled || option.disabled) return
    
    const newValue = [...value, option]
    onChange?.(newValue)
    setSearchValue("")
    inputRef.current?.focus()
  }

  const handleRemove = (optionToRemove: Option) => {
    if (disabled) return
    
    const newValue = value.filter(option => option.value !== optionToRemove.value)
    onChange?.(newValue)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && searchValue === "" && value.length > 0) {
      handleRemove(value[value.length - 1])
    }
    if (e.key === "Escape") {
      setIsOpen(false)
    }
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`flex min-h-10 w-full flex-wrap items-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 ${
          disabled ? "cursor-not-allowed opacity-50" : ""
        }`}
        onClick={() => !disabled && inputRef.current?.focus()}
      >
        {value.map((option) => (
          <div
            key={option.value}
            className="mr-1 mb-1 flex items-center rounded-sm bg-secondary px-2 py-1 text-xs"
          >
            <span>{option.label}</span>
            {!disabled && (
              <button
                type="button"
                className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRemove(option)
                }}
              >
                <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
              </button>
            )}
          </div>
        ))}
        <input
          ref={inputRef}
          type="text"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
          onKeyDown={handleKeyDown}
          placeholder={value.length === 0 ? placeholder : ""}
          className="flex-1 bg-transparent outline-none placeholder:text-muted-foreground"
          disabled={disabled}
        />
      </div>

      {isOpen && (
        <div className="absolute top-full z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-popover p-1 text-popover-foreground shadow-md">
          {availableOptions.length > 0 ? (
            availableOptions.map((option) => (
              <div
                key={option.value}
                className={`relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground ${
                  option.disabled ? "pointer-events-none opacity-50" : ""
                }`}
                onClick={() => handleSelect(option)}
              >
                {option.label}
              </div>
            ))
          ) : (
            <div className="py-6 text-center text-sm text-muted-foreground">
              No results found
            </div>
          )}
        </div>
      )}
    </div>
  )
}
