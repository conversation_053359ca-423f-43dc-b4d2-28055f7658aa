import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { ArrowLeft, RefreshCw, Eye, EyeOff } from 'lucide-react'

export default function BCELogin() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    captcha_answer: ''
  })
  const [captcha, setCaptcha] = useState({ question: '', loading: false })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    generateCaptcha()
  }, [])

  const generateCaptcha = async () => {
    setCaptcha({ question: '', loading: true })
    setError('') // Clear any previous errors
    try {
      const response = await fetch('http://localhost:8000/api/auth/generate-captcha/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      const data = await response.json()
      if (data.success) {
        setCaptcha({ question: data.question, loading: false })
      } else {
        setError(data.error || 'Failed to generate captcha')
        setCaptcha({ question: '', loading: false })
      }
    } catch (error) {
      setError('Network error. Please check your connection.')
      setCaptcha({ question: '', loading: false })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('http://localhost:8000/api/auth/bce-login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        navigate('/dashboard')
      } else {
        setError(data.error || 'Login failed')
        generateCaptcha() // Generate new captcha on failed attempt
        setFormData({ ...formData, captcha_answer: '' })
      }
    } catch (error) {
      setError('Network error. Please try again.')
      generateCaptcha()
      setFormData({ ...formData, captcha_answer: '' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="w-full max-w-md space-y-8 px-4">
        <div className="text-center space-y-4">
          <Link
            to="/"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>

          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
              BCE Internal Login
            </h1>
            <p className="text-muted-foreground">
              Access the internal talent management system
            </p>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-8 shadow-sm">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your email address"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 pr-10 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-card-foreground">
                  Security Question
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 p-3 bg-muted rounded-md border">
                    <span className="text-sm font-mono">
                      {captcha.loading ? 'Loading...' : captcha.question || 'Click refresh to generate'}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={generateCaptcha}
                    disabled={captcha.loading}
                    className="p-2 rounded-md border border-input hover:bg-muted transition-colors disabled:opacity-50"
                  >
                    <RefreshCw className={`h-4 w-4 ${captcha.loading ? 'animate-spin' : ''}`} />
                  </button>
                </div>
                <input
                  type="number"
                  name="captcha_answer"
                  value={formData.captcha_answer}
                  onChange={handleInputChange}
                  required
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter the answer"
                />
              </div>

              <button
                type="submit"
                disabled={loading || !captcha.question}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/90 h-10 px-4 py-2 w-full"
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
