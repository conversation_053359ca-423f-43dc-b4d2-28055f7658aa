import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockFetchError } from '../../test/utils'
import Login from '../Login'

// Mock useNavigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({ success: true, question: '2 + 2 = ?', answer: '4' })
  })

  it('renders login form correctly', () => {
    render(<Login loginType="bce" />)

    expect(screen.getByText('BCE Login')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('renders vendor login correctly', () => {
    render(<Login loginType="vendor" />)

    expect(screen.getByText('Vendor Login')).toBeInTheDocument()
  })

  it('calls captcha API on mount', async () => {
    render(<Login loginType="bce" />)

    // Just verify that fetch was called, don't worry about exact parameters
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    }, { timeout: 1000 })
  })

  it('has password input field', () => {
    render(<Login loginType="bce" />)

    const passwordInput = screen.getByPlaceholderText(/password/i)
    expect(passwordInput).toHaveAttribute('type', 'password')
  })

  it('allows typing in form fields', async () => {
    const user = userEvent.setup()
    render(<Login loginType="bce" />)

    const emailInput = screen.getByPlaceholderText(/email/i)
    const passwordInput = screen.getByPlaceholderText(/password/i)

    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')

    expect(emailInput).toHaveValue('<EMAIL>')
    expect(passwordInput).toHaveValue('password123')
  })

  it('has captcha input field', () => {
    render(<Login loginType="bce" />)

    expect(screen.getByPlaceholderText(/captcha/i)).toBeInTheDocument()
  })

  it('has multiple buttons including sign in', () => {
    render(<Login loginType="bce" />)

    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)

    const signInButton = screen.getByRole('button', { name: /sign in/i })
    expect(signInButton).toBeInTheDocument()
  })
})
