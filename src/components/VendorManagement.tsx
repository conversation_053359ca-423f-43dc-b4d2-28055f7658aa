import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Building, Plus, Edit, Trash2, Search, ArrowLeft, Users } from 'lucide-react'
import VendorDetailsModal from './VendorDetailsModal'
import CreateVendorModal from './CreateVendorModal'

interface Vendor {
  id: string
  name: string
  primary_email: string
  primary_contact: string
  website: string
  description: string
  users: string[]
  user_details?: User[]
}

interface User {
  id: string
  email: string
  full_name: string
  user_type: string
  is_active: boolean
}

interface PaginationData {
  current_page: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

interface VendorManagementProps {
  onBack?: () => void
}

export default function VendorManagement({ onBack }: VendorManagementProps) {
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showAddVendorModal, setShowAddVendorModal] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null)
  const [showVendorDetails, setShowVendorDetails] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    fetchVendors()
  }, [currentPage, searchTerm])

  const fetchVendors = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '10',
        ...(searchTerm && { search: searchTerm }),
      })

      const response = await fetch(`http://localhost:8000/api/vendor-management/vendors/?${params}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setVendors(data.data.vendors)
          setPagination(data.data.pagination)
        } else {
          setError('Failed to fetch vendors')
        }
      } else if (response.status === 403) {
        setError('Permission denied')
      } else {
        setError('Failed to fetch vendors')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleVendorClick = (vendor: Vendor) => {
    setSelectedVendor(vendor)
    setShowVendorDetails(true)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchVendors()
  }

  const handleDeleteVendor = async (vendorId: string) => {
    if (!window.confirm('Are you sure you want to delete this vendor?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:8000/api/vendor-management/vendors/${vendorId}/delete/`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          fetchVendors() // Refresh the list
        } else {
          setError(data.error || 'Failed to delete vendor')
        }
      } else {
        setError('Failed to delete vendor')
      }
    } catch (error) {
      setError('Network error')
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {onBack && (
              <button
                onClick={onBack}
                className="mr-4 p-2 rounded-md hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
            <div>
              <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                <Building className="h-6 w-6 mr-2" />
                Vendors
              </h2>
              <p className="text-muted-foreground">Manage vendor profiles and associated users</p>
            </div>
          </div>
          <button
            onClick={() => setShowAddVendorModal(true)}
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Vendor
          </button>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
          >
            Search
          </button>
        </form>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Vendors Table */}
      <div className="bg-card rounded-lg border border-border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Vendor Name</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Primary Email</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Primary Contact</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Users</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={5} className="px-4 py-8 text-center text-muted-foreground">
                    Loading vendors...
                  </td>
                </tr>
              ) : vendors.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-4 py-8 text-center text-muted-foreground">
                    No vendors found
                  </td>
                </tr>
              ) : (
                vendors.map((vendor) => (
                  <tr key={vendor.id} className="border-b border-border hover:bg-muted/50">
                    <td className="px-4 py-4">
                      <button
                        onClick={() => handleVendorClick(vendor)}
                        className="font-medium text-card-foreground hover:text-primary transition-colors text-left"
                      >
                        {vendor.name}
                      </button>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{vendor.primary_email}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{vendor.primary_contact || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {vendor.users?.length || 0}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleVendorClick(vendor)}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="View/Edit vendor"
                        >
                          <Edit className="h-4 w-4 text-muted-foreground" />
                        </button>
                        <button
                          onClick={() => handleDeleteVendor(vendor.id)}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Delete vendor"
                        >
                          <Trash2 className="h-4 w-4 text-muted-foreground" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination && pagination.total_pages > 1 && (
          <div className="flex items-center justify-between px-4 py-4 border-t border-border">
            <div className="text-sm text-muted-foreground">
              Showing {vendors.length} of {pagination.total_count} vendors
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={!pagination.has_previous}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Previous
              </button>
              <span className="text-sm text-muted-foreground">
                Page {pagination.current_page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!pagination.has_next}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Vendor Modal */}
      <CreateVendorModal
        isOpen={showAddVendorModal}
        onClose={() => setShowAddVendorModal(false)}
        onVendorCreated={fetchVendors}
      />

      {/* Vendor Details Modal */}
      <VendorDetailsModal
        vendor={selectedVendor}
        isOpen={showVendorDetails}
        onClose={() => {
          setShowVendorDetails(false)
          setSelectedVendor(null)
        }}
        onVendorUpdated={fetchVendors}
      />
    </div>
  )
}
