import { useState, useEffect } from 'react'
import { X, Users, Shield, Plus, Trash2, Edit } from 'lucide-react'

interface Group {
  id: number
  name: string
  members: User[]
  permissions: Permission[]
  members_count: number
  permissions_count: number
}

interface User {
  id: string
  email: string
  full_name: string
  user_type: string
  is_active: boolean
}

interface Permission {
  id: number
  name: string
  codename: string
  content_type: string
}

interface GroupDetailsModalProps {
  group: Group | null
  isOpen: boolean
  onClose: () => void
  onGroupUpdated: () => void
}

export default function GroupDetailsModal({ group, isOpen, onClose, onGroupUpdated }: GroupDetailsModalProps) {
  const [groupDetails, setGroupDetails] = useState<Group | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'members' | 'permissions'>('members')
  const [showAddMemberModal, setShowAddMemberModal] = useState(false)
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [selectedUserId, setSelectedUserId] = useState('')
  const [addingMember, setAddingMember] = useState(false)
  const [editingGroupName, setEditingGroupName] = useState(false)
  const [newGroupName, setNewGroupName] = useState('')
  const [savingGroupName, setSavingGroupName] = useState(false)

  useEffect(() => {
    if (isOpen && group) {
      fetchGroupDetails()
      setNewGroupName(group.name)
    }
  }, [isOpen, group])

  const fetchGroupDetails = async () => {
    if (!group) return

    setLoading(true)
    try {
      const response = await fetch(`http://localhost:8000/api/admin-controls/groups/${group.id}/`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setGroupDetails(data.group)
        } else {
          setError('Failed to fetch group details')
        }
      } else {
        setError('Failed to fetch group details')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableUsers = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/admin-controls/users/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Filter out users who are already in the group
          const currentMemberIds = groupDetails?.members.map(m => m.id) || []
          const available = data.data.users.filter((user: User) =>
            !currentMemberIds.includes(user.id) && user.is_active
          )
          setAvailableUsers(available)
        }
      }
    } catch (error) {
      console.error('Error fetching available users:', error)
    }
  }

  const addUserToGroup = async () => {
    if (!group || !selectedUserId) return

    setAddingMember(true)
    try {
      const response = await fetch(`http://localhost:8000/api/admin-controls/groups/${group.id}/add-user/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ user_id: selectedUserId })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setShowAddMemberModal(false)
          setSelectedUserId('')
          fetchGroupDetails() // Refresh group details
          onGroupUpdated() // Refresh parent list
        } else {
          setError(data.error || 'Failed to add user to group')
        }
      } else {
        setError('Failed to add user to group')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setAddingMember(false)
    }
  }

  const saveGroupName = async () => {
    if (!group || !newGroupName.trim()) return

    setSavingGroupName(true)
    try {
      const response = await fetch(`http://localhost:8000/api/admin-controls/groups/${group.id}/update/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ name: newGroupName.trim() })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setEditingGroupName(false)
          fetchGroupDetails() // Refresh group details
          onGroupUpdated() // Refresh parent list
        } else {
          setError(data.error || 'Failed to update group name')
        }
      } else {
        setError('Failed to update group name')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setSavingGroupName(false)
    }
  }

  const handleAddMemberClick = () => {
    fetchAvailableUsers()
    setShowAddMemberModal(true)
  }

  const removeUserFromGroup = async (userId: string) => {
    if (!group) return

    try {
      const response = await fetch(`http://localhost:8000/api/admin-controls/groups/${group.id}/remove-user/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ user_id: userId })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          fetchGroupDetails() // Refresh group details
          onGroupUpdated() // Refresh parent list
        } else {
          setError(data.error || 'Failed to remove user from group')
        }
      } else {
        setError('Failed to remove user from group')
      }
    } catch (error) {
      setError('Network error')
    }
  }

  if (!isOpen || !group) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex-1">
            {editingGroupName ? (
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  className="text-xl font-semibold bg-background border border-input rounded px-2 py-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') saveGroupName()
                    if (e.key === 'Escape') {
                      setEditingGroupName(false)
                      setNewGroupName(group.name)
                    }
                  }}
                  autoFocus
                />
                <button
                  onClick={saveGroupName}
                  disabled={savingGroupName || !newGroupName.trim()}
                  className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50"
                >
                  {savingGroupName ? 'Saving...' : 'Save'}
                </button>
                <button
                  onClick={() => {
                    setEditingGroupName(false)
                    setNewGroupName(group.name)
                  }}
                  className="px-2 py-1 text-xs border border-input rounded hover:bg-muted"
                >
                  Cancel
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <h3 className="text-xl font-semibold text-card-foreground">{group.name}</h3>
                <button
                  onClick={() => setEditingGroupName(true)}
                  className="p-1 rounded hover:bg-muted transition-colors"
                  title="Edit group name"
                >
                  <Edit className="h-3.5 w-3.5 text-muted-foreground" />
                </button>
              </div>
            )}
            <p className="text-sm text-muted-foreground">
              {groupDetails?.members_count || 0} members • {groupDetails?.permissions_count || 0} permissions
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-muted transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3 mb-4">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-6">
          <button
            onClick={() => setActiveTab('members')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'members'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted'
            }`}
          >
            <Users className="h-4 w-4 inline mr-2" />
            Members
          </button>
          <button
            onClick={() => setActiveTab('permissions')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'permissions'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted'
            }`}
          >
            <Shield className="h-4 w-4 inline mr-2" />
            Permissions
          </button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-muted-foreground">Loading...</div>
          </div>
        ) : (
          <>
            {/* Members Tab */}
            {activeTab === 'members' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-card-foreground">Group Members</h4>
                  <button
                    onClick={handleAddMemberClick}
                    className="inline-flex items-center px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Member
                  </button>
                </div>

                {groupDetails?.members && groupDetails.members.length > 0 ? (
                  <div className="bg-muted/20 rounded-lg border border-border">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-border">
                            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">User</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Type</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {groupDetails.members.map((member) => (
                            <tr key={member.id} className="border-b border-border hover:bg-muted/50">
                              <td className="px-4 py-4">
                                <div>
                                  <div className="text-sm font-medium text-card-foreground">{member.full_name}</div>
                                  <div className="text-sm text-muted-foreground">{member.email}</div>
                                </div>
                              </td>
                              <td className="px-4 py-4">
                                <span className="text-sm text-muted-foreground capitalize">{member.user_type}</span>
                              </td>
                              <td className="px-4 py-4">
                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                  member.is_active 
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                }`}>
                                  {member.is_active ? 'Active' : 'Inactive'}
                                </span>
                              </td>
                              <td className="px-4 py-4">
                                <button
                                  onClick={() => removeUserFromGroup(member.id)}
                                  className="p-1 rounded hover:bg-muted transition-colors"
                                  title="Remove from group"
                                >
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No members in this group yet.
                  </div>
                )}
              </div>
            )}

            {/* Permissions Tab */}
            {activeTab === 'permissions' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-card-foreground">Group Permissions</h4>
                  <button className="inline-flex items-center px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Permission
                  </button>
                </div>

                <div className="bg-muted/10 border border-border rounded-lg p-4">
                  <p className="text-sm text-muted-foreground mb-4">
                    Permission management functionality will be implemented in a future update. 
                    For now, you can manage permissions through the Django admin panel.
                  </p>
                  
                  {groupDetails?.permissions && groupDetails.permissions.length > 0 ? (
                    <div className="space-y-2">
                      {groupDetails.permissions.map((permission) => (
                        <div key={permission.id} className="flex items-center justify-between p-2 bg-background rounded border">
                          <div>
                            <div className="text-sm font-medium text-card-foreground">{permission.name}</div>
                            <div className="text-xs text-muted-foreground">{permission.codename} • {permission.content_type}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No permissions assigned to this group.
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        )}

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
          >
            Close
          </button>
        </div>
      </div>

      {/* Add Member Modal */}
      {showAddMemberModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-card rounded-lg border border-border p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-card-foreground mb-4">Add Member to {group?.name}</h3>

            {availableUsers.length > 0 ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-card-foreground mb-2">
                    Select User
                  </label>
                  <select
                    value={selectedUserId}
                    onChange={(e) => setSelectedUserId(e.target.value)}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="">Select a user...</option>
                    {availableUsers.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.full_name} ({user.email})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground mb-4">
                No available users to add to this group.
              </p>
            )}

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => {
                  setShowAddMemberModal(false)
                  setSelectedUserId('')
                  setError('')
                }}
                className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
                disabled={addingMember}
              >
                Cancel
              </button>
              {availableUsers.length > 0 && (
                <button
                  onClick={addUserToGroup}
                  disabled={addingMember || !selectedUserId}
                  className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {addingMember ? 'Adding...' : 'Add Member'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
