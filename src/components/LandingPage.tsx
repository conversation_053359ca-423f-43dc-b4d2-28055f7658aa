import { Link } from 'react-router-dom'

export default function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="text-center space-y-8 max-w-2xl mx-auto px-4">
        <div className="space-y-4">
          <h1 className="text-6xl font-bold text-foreground tracking-tight">
            Talent Hero
          </h1>
          <p className="text-xl text-muted-foreground font-mono">
            v3.11
          </p>
        </div>
        
        <div className="space-y-4">
          <h2 className="text-3xl font-semibold text-foreground">
            Coming Soon
          </h2>
          <p className="text-lg text-muted-foreground max-w-lg mx-auto">
            An internal tool designed to streamline Talent Acquisition and Talent Management processes within organizations.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
          <Link
            to="/vendor"
            className="inline-flex items-center justify-center rounded-lg bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
          >
            Vendor Login
          </Link>
          <Link
            to="/bce"
            className="inline-flex items-center justify-center rounded-lg bg-secondary px-8 py-3 text-sm font-medium text-secondary-foreground shadow transition-colors hover:bg-secondary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
          >
            BCE Internal Login
          </Link>
        </div>
      </div>
    </div>
  )
}
