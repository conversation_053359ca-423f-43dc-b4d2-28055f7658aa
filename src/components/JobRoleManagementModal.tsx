import { useState, useEffect } from 'react'
import { X, Briefcase, Plus, Edit, Trash2, FileText, Upload } from 'lucide-react'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
  job_description: string
  description_type: string
  has_file: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

interface JobRoleManagementModalProps {
  isOpen: boolean
  onClose: () => void
  onJobRoleCreated: () => void
}

export default function JobRoleManagementModal({ isOpen, onClose, onJobRoleCreated }: JobRoleManagementModalProps) {
  const [jobRoles, setJobRoles] = useState<JobRole[]>([])
  const [selectedRole, setSelectedRole] = useState<JobRole | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  // Form states
  const [roleName, setRoleName] = useState('')
  const [yearsOfExperience, setYearsOfExperience] = useState('')
  const [jobDescription, setJobDescription] = useState('')
  const [descriptionType, setDescriptionType] = useState<'text' | 'file'>('text')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [saving, setSaving] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  useEffect(() => {
    if (isOpen) {
      fetchJobRoles()
      resetForm()
    }
  }, [isOpen])

  useEffect(() => {
    if (selectedRole) {
      setRoleName(selectedRole.role_name)
      setYearsOfExperience(selectedRole.years_of_experience.toString())
      setJobDescription(selectedRole.job_description)
      setDescriptionType(selectedRole.description_type as 'text' | 'file')
      setIsEditing(true)
    }
  }, [selectedRole])

  const resetForm = () => {
    setRoleName('')
    setYearsOfExperience('')
    setJobDescription('')
    setDescriptionType('text')
    setSelectedFile(null)
    setError('')
    setIsEditing(false)
    setSelectedRole(null)
  }

  const fetchJobRoles = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/job-management/job-roles/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobRoles(data.data.job_roles)
        } else {
          setError('Failed to fetch job roles')
        }
      } else {
        setError('Failed to fetch job roles')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a PDF, DOC, DOCX, or TXT file')
        return
      }
      
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB')
        return
      }
      
      setSelectedFile(file)
      setError('')
    }
  }

  const saveJobRole = async () => {
    if (!roleName.trim()) {
      setError('Role name is required')
      return
    }

    if (!yearsOfExperience.trim()) {
      setError('Years of experience is required')
      return
    }

    const years = parseInt(yearsOfExperience)
    if (isNaN(years) || years < 0) {
      setError('Years of experience must be a valid non-negative number')
      return
    }

    if (descriptionType === 'text' && !jobDescription.trim()) {
      setError('Job description is required when using text input')
      return
    }

    if (descriptionType === 'file' && !selectedFile && !isEditing) {
      setError('Please select a file when using file upload')
      return
    }

    setSaving(true)
    try {
      let requestData: any = {
        role_name: roleName.trim(),
        years_of_experience: years,
        description_type: descriptionType
      }

      if (descriptionType === 'text') {
        requestData.job_description = jobDescription.trim()
      }

      if (descriptionType === 'file') {
        setError('File upload functionality will be implemented soon. Please use text input for now.')
        setSaving(false)
        return
      }

      const url = isEditing 
        ? `http://localhost:8000/api/job-management/job-roles/${selectedRole?.id}/update/`
        : 'http://localhost:8000/api/job-management/job-roles/create/'
      
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          resetForm()
          fetchJobRoles()
          onJobRoleCreated()
        } else {
          setError(data.error || `Failed to ${isEditing ? 'update' : 'create'} job role`)
        }
      } else {
        setError(`Failed to ${isEditing ? 'update' : 'create'} job role`)
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setSaving(false)
    }
  }

  const deleteJobRole = async (roleId: string) => {
    if (!window.confirm('Are you sure you want to delete this job role?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:8000/api/job-management/job-roles/${roleId}/delete/`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          fetchJobRoles()
          if (selectedRole?.id === roleId) {
            resetForm()
          }
        } else {
          setError(data.error || 'Failed to delete job role')
        }
      } else {
        setError('Failed to delete job role')
      }
    } catch (error) {
      setError('Network error')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border w-full max-w-6xl max-h-[90vh] flex">
        {/* Left Panel - Job Roles List */}
        <div className="w-1/2 border-r border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <h3 className="text-lg font-semibold text-card-foreground flex items-center">
              <Briefcase className="h-5 w-5 mr-2" />
              Existing Job Roles
            </h3>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {loading ? (
              <div className="text-center text-muted-foreground">Loading job roles...</div>
            ) : jobRoles.length === 0 ? (
              <div className="text-center text-muted-foreground">
                <Briefcase className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No job roles found</p>
                <p className="text-sm">Create your first job role using the form on the right</p>
              </div>
            ) : (
              <div className="space-y-2">
                {jobRoles.map((role) => (
                  <div
                    key={role.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedRole?.id === role.id
                        ? 'border-primary bg-primary/10'
                        : 'border-border hover:bg-muted/50'
                    }`}
                    onClick={() => setSelectedRole(role)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-card-foreground">{role.role_name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {role.years_of_experience} years experience
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {role.description_type === 'file' ? 'File description' : 'Text description'}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedRole(role)
                          }}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Edit role"
                        >
                          <Edit className="h-3 w-3 text-muted-foreground" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteJobRole(role.id)
                          }}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Delete role"
                        >
                          <Trash2 className="h-3 w-3 text-muted-foreground" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Create/Edit Form */}
        <div className="w-1/2 flex flex-col">
          <div className="p-4 border-b border-border flex items-center justify-between">
            <h3 className="text-lg font-semibold text-card-foreground">
              {isEditing ? 'Edit Job Role' : 'Create Job Role'}
            </h3>
            <button
              onClick={handleClose}
              className="p-1 rounded hover:bg-muted transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {error && (
              <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
                {error}
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label htmlFor="roleName" className="block text-sm font-medium mb-1">
                  Role Name <span className="text-destructive">*</span>
                </label>
                <input
                  id="roleName"
                  type="text"
                  value={roleName}
                  onChange={(e) => setRoleName(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="e.g., Senior Software Engineer"
                />
              </div>

              <div>
                <label htmlFor="yearsOfExperience" className="block text-sm font-medium mb-1">
                  Years of Experience <span className="text-destructive">*</span>
                </label>
                <input
                  id="yearsOfExperience"
                  type="number"
                  min="0"
                  value={yearsOfExperience}
                  onChange={(e) => setYearsOfExperience(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Job Description <span className="text-destructive">*</span>
                </label>
                
                <div className="flex space-x-4 mb-3">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="descriptionType"
                      value="text"
                      checked={descriptionType === 'text'}
                      onChange={(e) => setDescriptionType(e.target.value as 'text' | 'file')}
                      className="mr-2"
                    />
                    <FileText className="h-4 w-4 mr-1" />
                    Text Input
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="descriptionType"
                      value="file"
                      checked={descriptionType === 'file'}
                      onChange={(e) => setDescriptionType(e.target.value as 'text' | 'file')}
                      className="mr-2"
                    />
                    <Upload className="h-4 w-4 mr-1" />
                    File Upload
                  </label>
                </div>

                {descriptionType === 'text' ? (
                  <textarea
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Enter detailed job description..."
                    rows={8}
                  />
                ) : (
                  <div>
                    <input
                      type="file"
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileChange}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Supported formats: PDF, DOC, DOCX, TXT (max 5MB)
                    </p>
                    {selectedFile && (
                      <div className="mt-2 p-2 bg-muted rounded-md">
                        <p className="text-sm text-card-foreground">
                          Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-border flex justify-between">
            <button
              onClick={resetForm}
              className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
              disabled={saving}
            >
              {isEditing ? 'Cancel Edit' : 'Clear Form'}
            </button>
            <button
              onClick={saveJobRole}
              disabled={saving || !roleName.trim() || !yearsOfExperience.trim()}
              className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? 'Saving...' : isEditing ? 'Update Role' : 'Create Role'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
