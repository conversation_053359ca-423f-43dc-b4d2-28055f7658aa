import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Home, Users, Settings, LogOut, Menu, X, Building, Briefcase, UserCheck } from 'lucide-react'
import UserManagement from './UserManagement'
import GroupManagement from './GroupManagement'
import VendorManagement from './VendorManagement'
import JobManagement from './JobManagement'
import CandidateManagement from './CandidateManagement'

interface User {
  id: string
  email: string
  employee_id: string
  full_name: string
  user_type: string
  role_category: string
  can_access_admin: boolean
}

interface DashboardData {
  user: User
  permissions: {
    can_access_admin: boolean
    can_manage_users: boolean
    can_manage_admins: boolean
    can_view_admin_panel: boolean
    can_manage_vendors: boolean
  }
  stats: {
    total_users: number
    active_users: number
    total_admins: number
    total_vendors: number
  }
}

export default function Dashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeTab, setActiveTab] = useState('home')
  const navigate = useNavigate()

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/core/dashboard/', {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setDashboardData(data.data)
        } else {
          navigate('/bce')
        }
      } else {
        navigate('/bce')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      navigate('/bce')
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('http://localhost:8000/api/auth/logout/', {
        method: 'POST',
        credentials: 'include',
      })
      navigate('/bce')
    } catch (error) {
      console.error('Error logging out:', error)
      navigate('/bce')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Failed to load dashboard</p>
          <button
            onClick={() => navigate('/bce')}
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Back to Login
          </button>
        </div>
      </div>
    )
  }

  const sidebarItems = [
    { id: 'home', label: 'Home', icon: Home },
    ...(dashboardData.permissions.can_access_admin ? [
      { id: 'jobs', label: 'Job Management', icon: Briefcase },
      { id: 'candidates', label: 'Candidate Management', icon: UserCheck },
      { id: 'vendors', label: 'Vendor Management', icon: Building },
      { id: 'admin', label: 'Admin Panel', icon: Settings }
    ] : []),
  ]

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-card border-r border-border transition-all duration-300 flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <h1 className="text-xl font-bold text-card-foreground">Talent Hero</h1>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md hover:bg-muted transition-colors"
            >
              {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => {
              const Icon = item.icon
              return (
                <li key={item.id}>
                  <button
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {sidebarOpen && <span className="ml-3">{item.label}</span>}
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* User Info & Logout */}
        <div className="p-4 border-t border-border">
          {sidebarOpen && (
            <div className="mb-3">
              <p className="text-sm font-medium text-card-foreground">{dashboardData.user.full_name}</p>
              <p className="text-xs text-muted-foreground">{dashboardData.user.employee_id}</p>
            </div>
          )}
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
          >
            <LogOut className="h-4 w-4" />
            {sidebarOpen && <span className="ml-3">Logout</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-card border-b border-border p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-card-foreground">
                {activeTab === 'home' ? 'Dashboard' :
                 activeTab === 'admin' ? 'Admin Panel' :
                 activeTab === 'users' ? 'User Management' :
                 activeTab === 'groups' ? 'Group Management' :
                 activeTab === 'vendors' ? 'Vendor Management' :
                 activeTab === 'jobs' ? 'Job Management' :
                 activeTab === 'candidates' ? 'Candidate Management' : 'Dashboard'}
              </h2>
              <p className="text-muted-foreground">
                Welcome back, {dashboardData.user.full_name}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                {dashboardData.user.department} • {dashboardData.user.position}
              </span>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          {activeTab === 'home' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {dashboardData.permissions.can_access_admin && (
                  <>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Total Users</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_users}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Active Users</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.active_users}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Admins</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_admins}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Vendors</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_vendors}</p>
                    </div>
                  </>
                )}
              </div>
              
              <div className="bg-card rounded-lg border border-border p-6">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">
                  Talent Hero v3.11
                </h3>
                <div className="space-y-3">
                  <p className="text-muted-foreground">
                    Your comprehensive talent acquisition and management platform. Use the sidebar to navigate through different sections.
                  </p>
                  <div className="bg-muted/50 rounded-md p-4">
                    <h4 className="text-sm font-medium text-card-foreground mb-2">Your Profile</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Username:</span>
                        <span className="ml-2 font-mono text-card-foreground">{dashboardData.user.email.split('@')[0]}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Employee ID:</span>
                        <span className="ml-2 font-mono text-card-foreground">{dashboardData.user.employee_id}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Role:</span>
                        <span className="ml-2 text-card-foreground">{dashboardData.user.user_type}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Department:</span>
                        <span className="ml-2 text-card-foreground">{dashboardData.user.department}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'admin' && dashboardData.permissions.can_access_admin && (
            <div className="space-y-6">
              <div className="bg-card rounded-lg border border-border p-6">
                <p className="text-muted-foreground mb-6">
                  Manage users, permissions, and system settings.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <button
                    onClick={() => setActiveTab('users')}
                    className="block p-4 rounded-lg border border-border hover:bg-muted transition-colors text-left"
                  >
                    <Users className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium text-card-foreground">User Management</h4>
                    <p className="text-sm text-muted-foreground">Create, edit, and manage user accounts</p>
                  </button>

                  <button
                    onClick={() => setActiveTab('groups')}
                    className="block p-4 rounded-lg border border-border hover:bg-muted transition-colors text-left"
                  >
                    <Settings className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium text-card-foreground">Groups & Permissions</h4>
                    <p className="text-sm text-muted-foreground">Manage user groups and permissions</p>
                  </button>

                  <div className="p-4 rounded-lg border border-border opacity-50">
                    <Settings className="h-8 w-8 text-muted-foreground mb-2" />
                    <h4 className="font-medium text-muted-foreground">System Settings</h4>
                    <p className="text-sm text-muted-foreground">Coming soon...</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'users' && dashboardData.permissions.can_access_admin && (
            <UserManagement onBack={() => setActiveTab('admin')} />
          )}

          {activeTab === 'groups' && dashboardData.permissions.can_access_admin && (
            <GroupManagement onBack={() => setActiveTab('admin')} />
          )}

          {activeTab === 'vendors' && dashboardData.permissions.can_access_admin && (
            <VendorManagement />
          )}

          {activeTab === 'jobs' && dashboardData.permissions.can_access_admin && (
            <JobManagement />
          )}

          {activeTab === 'candidates' && dashboardData.permissions.can_access_admin && (
            <CandidateManagement />
          )}
        </main>
      </div>
    </div>
  )
}
