import { useState, useEffect } from 'react'
import { X, Building, Users } from 'lucide-react'

interface User {
  id: string
  email: string
  full_name: string
  employee_id: string
  department: string
  position: string
}

interface CreateVendorModalProps {
  isOpen: boolean
  onClose: () => void
  onVendorCreated: () => void
}

export default function CreateVendorModal({ isOpen, onClose, onVendorCreated }: CreateVendorModalProps) {
  const [name, setName] = useState('')
  const [primaryEmail, setPrimaryEmail] = useState('')
  const [primaryContact, setPrimaryContact] = useState('')
  const [website, setWebsite] = useState('')
  const [description, setDescription] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [availableUsers, setAvailableUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [creating, setCreating] = useState(false)

  useEffect(() => {
    if (isOpen) {
      fetchVendorUsers()
      resetForm()
    }
  }, [isOpen])

  const resetForm = () => {
    setName('')
    setPrimaryEmail('')
    setPrimaryContact('')
    setWebsite('')
    setDescription('')
    setSelectedUsers([])
    setError('')
  }

  const fetchVendorUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/vendor-management/vendor-users/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAvailableUsers(data.data)
        } else {
          setError('Failed to fetch vendor users')
        }
      } else {
        setError('Failed to fetch vendor users')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleUserSelection = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId))
    } else {
      setSelectedUsers([...selectedUsers, userId])
    }
  }

  const createVendor = async () => {
    if (!name.trim()) {
      setError('Vendor name is required')
      return
    }

    if (!primaryEmail.trim()) {
      setError('Primary email is required')
      return
    }

    setCreating(true)
    try {
      const response = await fetch('http://localhost:8000/api/vendor-management/vendors/create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: name.trim(),
          primary_email: primaryEmail.trim(),
          primary_contact: primaryContact.trim(),
          website: website.trim(),
          description: description.trim(),
          users: selectedUsers
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          onClose()
          onVendorCreated()
        } else {
          setError(data.error || 'Failed to create vendor')
        }
      } else {
        setError('Failed to create vendor')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-card-foreground flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Add New Vendor
          </h3>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-muted transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-1">
              Vendor Name <span className="text-destructive">*</span>
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Enter vendor name"
            />
          </div>

          <div>
            <label htmlFor="primaryEmail" className="block text-sm font-medium mb-1">
              Primary Email <span className="text-destructive">*</span>
            </label>
            <input
              id="primaryEmail"
              type="email"
              value={primaryEmail}
              onChange={(e) => setPrimaryEmail(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Enter primary email"
            />
          </div>

          <div>
            <label htmlFor="primaryContact" className="block text-sm font-medium mb-1">
              Primary Contact Number
            </label>
            <input
              id="primaryContact"
              type="text"
              value={primaryContact}
              onChange={(e) => setPrimaryContact(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Enter primary contact number"
            />
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium mb-1">
              Website (Optional)
            </label>
            <input
              id="website"
              type="text"
              value={website}
              onChange={(e) => setWebsite(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Enter website URL"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-1">
              Description (Optional)
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Enter vendor description"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 flex items-center">
              <Users className="h-4 w-4 mr-1" />
              Vendor Users
            </label>
            {loading ? (
              <div className="text-sm text-muted-foreground">Loading users...</div>
            ) : availableUsers.length === 0 ? (
              <div className="text-sm text-muted-foreground">No vendor users available</div>
            ) : (
              <div className="max-h-40 overflow-y-auto border border-input rounded-md p-2">
                {availableUsers.map((user) => (
                  <div key={user.id} className="flex items-center py-1">
                    <input
                      type="checkbox"
                      id={`user-${user.id}`}
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleUserSelection(user.id)}
                      className="mr-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <label htmlFor={`user-${user.id}`} className="text-sm">
                      {user.full_name} ({user.email})
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
            disabled={creating}
          >
            Cancel
          </button>
          <button
            onClick={createVendor}
            disabled={creating || !name.trim() || !primaryEmail.trim()}
            className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? 'Creating...' : 'Create Vendor'}
          </button>
        </div>
      </div>
    </div>
  )
}
