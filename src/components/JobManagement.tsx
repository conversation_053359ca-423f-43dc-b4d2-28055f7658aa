import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Briefcase, Plus, Search, ArrowLeft, Building, Clock, MapPin, Calendar, User, Users, AlertTriangle } from 'lucide-react'
import JobRoleManagementModal from './JobRoleManagementModal'
import CreateJobModal from './CreateJobModal'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface Job {
  id: string
  job_title: string
  job_role_id: string
  job_role?: JobRole
  company_name: string
  location: string
  employment_type: string
  salary_range: string
  is_active: boolean
  is_published: boolean
  created_at: string
}

interface PaginationData {
  current_page: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

interface JobManagementProps {
  onBack?: () => void
}

export default function JobManagement({ onBack }: JobManagementProps) {
  const [jobs, setJobs] = useState<Job[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showRoleManagementModal, setShowRoleManagementModal] = useState(false)
  const [showCreateJobModal, setShowCreateJobModal] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    fetchJobs()
  }, [currentPage, searchTerm])

  const fetchJobs = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '10',
        ...(searchTerm && { search: searchTerm }),
      })

      const response = await fetch(`http://localhost:8000/api/job-management/jobs/?${params}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobs(data.data.jobs)
          setPagination(data.data.pagination)
        } else {
          setError('Failed to fetch jobs')
        }
      } else if (response.status === 403) {
        setError('Permission denied')
      } else {
        setError('Failed to fetch jobs')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchJobs()
  }

  const handleCreateJob = () => {
    setShowCreateJobModal(true)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {onBack && (
              <button
                onClick={onBack}
                className="mr-4 p-2 rounded-md hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
            <div>
              <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                <Briefcase className="h-6 w-6 mr-2" />
                Job Management
              </h2>
              <p className="text-muted-foreground">Manage job roles and job postings</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowRoleManagementModal(true)}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Role
            </button>
            <button
              onClick={handleCreateJob}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Job
            </button>
          </div>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search jobs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
          >
            Search
          </button>
        </form>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Jobs List - Horizontally Scrollable */}
      <div className="bg-card rounded-lg border border-border overflow-x-auto">
        {loading ? (
          <div className="p-8 text-center text-muted-foreground">
            Loading jobs...
          </div>
        ) : jobs.length === 0 ? (
          <div className="p-8 text-center">
            <Briefcase className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-card-foreground mb-2">No jobs found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? 'No jobs match your search criteria.' : 'Get started by creating your first job role and job posting.'}
            </p>
            <div className="flex justify-center space-x-2">
              <button
                onClick={() => setShowRoleManagementModal(true)}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-9 px-3"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Role
              </button>
              <button
                onClick={handleCreateJob}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Job
              </button>
            </div>
          </div>
        ) : (
          <div className="min-w-max">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[150px]">Ticket ID</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[200px]">Job Title</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[120px]">Date</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[150px]">Recruiter</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[150px]">TA Incharge</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[150px]">Client</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[200px]">Interview Panel</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[120px]">Sourcing Type</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[100px]">Priority</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[120px]">Applied</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[120px]">Interviewed</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[120px]">Availability</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground min-w-[100px]">Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobs.map((job) => (
                  <tr key={job.id} className="border-b border-border hover:bg-muted/50">
                    <td className="px-4 py-4 text-sm">
                      <span className="font-mono text-muted-foreground">TH-2025-001</span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-card-foreground">{job.job_title}</span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          job.is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {job.is_published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                      {job.job_role && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {job.job_role.role_name} ({job.job_role.years_of_experience} years)
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        2025-01-15
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        John Doe
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        Jane Smith
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Building className="h-3 w-3 mr-1" />
                        Tech Corp
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        Panel A, Panel B
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Vendor
                      </span>
                    </td>
                    <td className="px-4 py-4 text-sm">
                      <div className="flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1 text-red-500" />
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          High
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-center">
                      <span className="font-medium">25</span>
                    </td>
                    <td className="px-4 py-4 text-sm text-center">
                      <span className="font-medium">8</span>
                    </td>
                    <td className="px-4 py-4 text-sm">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Vacant
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-1">
                        <button
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Edit job"
                        >
                          <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" />
                          </svg>
                        </button>
                        <button
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Delete job"
                        >
                          <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {pagination && pagination.total_pages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-border">
            <div className="text-sm text-muted-foreground">
              Showing {jobs.length} of {pagination.total_count} jobs
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={!pagination.has_previous}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Previous
              </button>
              <span className="text-sm text-muted-foreground">
                Page {pagination.current_page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!pagination.has_next}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Job Role Management Modal */}
      <JobRoleManagementModal
        isOpen={showRoleManagementModal}
        onClose={() => setShowRoleManagementModal(false)}
        onJobRoleCreated={fetchJobs}
      />

      {/* Create Job Modal */}
      <CreateJobModal
        isOpen={showCreateJobModal}
        onClose={() => setShowCreateJobModal(false)}
        onJobCreated={fetchJobs}
      />
    </div>
  )
}
