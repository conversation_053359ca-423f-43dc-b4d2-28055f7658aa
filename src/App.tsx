import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import LandingPage from './components/LandingPage'
import VendorLogin from './components/VendorLogin'
import BCELogin from './components/BCELogin'
import Dashboard from './components/Dashboard'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-background text-foreground">

        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/vendor" element={<VendorLogin />} />
          <Route path="/bce" element={<BCELogin />} />
          <Route path="/dashboard" element={<Dashboard />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
