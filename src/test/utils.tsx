import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { vi } from 'vitest'

// Mock user data for testing
export const mockUser = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  email: '<EMAIL>',
  full_name: 'Test User',
  first_name: 'Test',
  last_name: 'User',
  user_type: 'admin',
  role_category: 'employee',
  department: 'IT',
  position: 'Developer',
  phone_number: '+1234567890',
  employee_id: 'EMP001',
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  last_login: '2024-01-01T12:00:00Z',
  can_deactivate: true,
  groups: [{ id: 1, name: 'Developers' }],
}

// Mock group data for testing
export const mockGroup = {
  id: 1,
  name: 'Test Group',
  permissions_count: 5,
  users_count: 3,
  members: [mockUser],
  permissions: [
    {
      id: 1,
      name: 'Can add user',
      codename: 'add_user',
      content_type: 'user',
    },
  ],
}

// Mock dashboard data
export const mockDashboardData = {
  user: mockUser,
  stats: {
    total_users: 10,
    active_users: 8,
    total_groups: 3,
    pending_approvals: 2,
  },
  permissions: {
    can_access_admin: true,
    can_create_users: true,
    can_manage_groups: true,
  },
}

// Mock API responses
export const mockApiResponse = {
  success: true,
  data: {},
  message: 'Success',
}

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <BrowserRouter>{children}</BrowserRouter>
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Mock fetch responses
export const mockFetch = (response: any, ok = true, status = 200) => {
  return vi.fn().mockResolvedValue({
    ok,
    status,
    json: () => Promise.resolve(response),
    text: () => Promise.resolve(JSON.stringify(response)),
  })
}

// Mock fetch error
export const mockFetchError = (error: string) => {
  return vi.fn().mockRejectedValue(new Error(error))
}

// Wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export * from '@testing-library/react'
export { customRender as render }
