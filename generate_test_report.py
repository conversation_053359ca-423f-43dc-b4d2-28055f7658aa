#!/usr/bin/env python3
"""
Generate HTML test report from test results
"""
import json
import os
from datetime import datetime
from pathlib import Path


class TestReportGenerator:
    def __init__(self):
        self.test_results_dir = Path('test-results')
        self.test_results_dir.mkdir(exist_ok=True)
    
    def load_latest_results(self):
        """Load the latest test results"""
        latest_file = self.test_results_dir / 'latest-test-report.json'
        if latest_file.exists():
            with open(latest_file, 'r') as f:
                return json.load(f)
        return None
    
    def generate_html_report(self, results):
        """Generate HTML report from test results"""
        if not results:
            return "<html><body><h1>No test results found</h1></body></html>"
        
        summary = results.get('summary', {})
        frontend = results.get('frontend', {})
        backend = results.get('backend', {})
        
        # Status colors
        status_colors = {
            'passed': '#28a745',
            'failed': '#dc3545',
            'error': '#ffc107',
            'timeout': '#6c757d'
        }
        
        overall_color = status_colors.get(summary.get('overall_status', 'error'), '#6c757d')
        frontend_color = status_colors.get(summary.get('frontend_status', 'error'), '#6c757d')
        backend_color = status_colors.get(summary.get('backend_status', 'error'), '#6c757d')
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talent Hero Test Report</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .summary {{
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        .status-card {{
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }}
        .status-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }}
        .status-card p {{
            margin: 0;
            font-size: 1.5em;
            text-transform: uppercase;
        }}
        .details {{
            padding: 0 30px 30px 30px;
        }}
        .section {{
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }}
        .section-header {{
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            font-size: 1.1em;
        }}
        .section-content {{
            padding: 20px;
        }}
        .test-output {{
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }}
        .timestamp {{
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }}
        .error {{
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Talent Hero Test Report</h1>
            <p>Comprehensive Testing Results</p>
        </div>
        
        <div class="summary">
            <div class="status-card" style="background-color: {overall_color}">
                <h3>Overall Status</h3>
                <p>{summary.get('overall_status', 'Unknown').upper()}</p>
            </div>
            <div class="status-card" style="background-color: {frontend_color}">
                <h3>Frontend Tests</h3>
                <p>{summary.get('frontend_status', 'Unknown').upper()}</p>
            </div>
            <div class="status-card" style="background-color: {backend_color}">
                <h3>Backend Tests</h3>
                <p>{summary.get('backend_status', 'Unknown').upper()}</p>
            </div>
        </div>
        
        <div class="details">
            <div class="section">
                <div class="section-header">🎨 Frontend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> {frontend.get('status', 'Unknown')}</p>
                    <p><strong>Return Code:</strong> {frontend.get('return_code', 'N/A')}</p>
                    
                    {self._generate_frontend_details(frontend)}
                    
                    {self._generate_output_section('Frontend Output', frontend.get('stdout', ''))}
                    {self._generate_error_section('Frontend Errors', frontend.get('stderr', ''))}
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">⚙️ Backend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> {backend.get('status', 'Unknown')}</p>
                    <p><strong>Return Code:</strong> {backend.get('return_code', 'N/A')}</p>
                    
                    {self._generate_output_section('Backend Output', backend.get('stdout', ''))}
                    {self._generate_error_section('Backend Errors', backend.get('stderr', ''))}
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_frontend_details(self, frontend):
        """Generate frontend-specific details"""
        test_results = frontend.get('test_results', {})
        if not test_results:
            return ""
        
        # Extract test statistics if available
        if 'testResults' in test_results:
            total_files = len(test_results['testResults'])
            total_tests = sum(len(test_file.get('assertionResults', [])) 
                            for test_file in test_results['testResults'])
            passed_tests = sum(len([test for test in test_file.get('assertionResults', []) 
                                  if test.get('status') == 'passed']) 
                             for test_file in test_results['testResults'])
            
            return f"""
                <p><strong>Test Files:</strong> {total_files}</p>
                <p><strong>Total Tests:</strong> {total_tests}</p>
                <p><strong>Passed Tests:</strong> {passed_tests}</p>
                <p><strong>Failed Tests:</strong> {total_tests - passed_tests}</p>
            """
        
        return ""
    
    def _generate_output_section(self, title, output):
        """Generate output section"""
        if not output or not output.strip():
            return ""
        
        return f"""
            <h4>{title}</h4>
            <div class="test-output">{output}</div>
        """
    
    def _generate_error_section(self, title, errors):
        """Generate error section"""
        if not errors or not errors.strip():
            return ""
        
        return f"""
            <h4>{title}</h4>
            <div class="error">{errors}</div>
        """
    
    def generate_report(self):
        """Generate and save HTML report"""
        results = self.load_latest_results()
        html_content = self.generate_html_report(results)
        
        # Save HTML report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.test_results_dir / f'test-report-{timestamp}.html'
        latest_report_file = self.test_results_dir / 'latest-test-report.html'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        with open(latest_report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"📊 HTML report generated: {report_file}")
        print(f"📊 Latest report: {latest_report_file}")
        
        return report_file


def main():
    generator = TestReportGenerator()
    generator.generate_report()


if __name__ == '__main__':
    main()
