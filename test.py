#!/usr/bin/env python3
"""
Main test script for Talent Hero application
Runs comprehensive tests and generates reports
"""
import subprocess
import sys
import os
from pathlib import Path


def main():
    """Main test execution"""
    print("🚀 Talent Hero Comprehensive Test Suite")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    # Ensure test results directory exists
    test_results_dir = project_root / 'test-results'
    test_results_dir.mkdir(exist_ok=True)
    
    try:
        # Run the comprehensive test runner
        print("Running comprehensive test suite...")
        result = subprocess.run([
            sys.executable, 'run_tests.py'
        ], cwd=project_root)
        
        # Generate HTML report
        print("\nGenerating HTML report...")
        subprocess.run([
            sys.executable, 'generate_test_report.py'
        ], cwd=project_root)
        
        print("\n🎉 Test execution completed!")
        print(f"📊 Check test-results/ directory for detailed reports")
        print(f"📊 Open test-results/latest-test-report.html for visual report")
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n❌ Test execution interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error during test execution: {e}")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
