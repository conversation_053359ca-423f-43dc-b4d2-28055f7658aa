#!/usr/bin/env python3
"""
Open the test dashboard in browser
"""
import webbrowser
import os
from pathlib import Path


def open_dashboard():
    """Open the test dashboard in the default browser"""
    project_root = Path(__file__).parent
    dashboard_file = project_root / 'test-results' / 'dashboard.html'
    
    if not dashboard_file.exists():
        print("📊 Creating test dashboard...")
        os.system('python create_test_dashboard.py')
    
    if dashboard_file.exists():
        dashboard_url = f"file://{dashboard_file.absolute()}"
        print(f"🌐 Opening test dashboard: {dashboard_url}")
        webbrowser.open(dashboard_url)
    else:
        print("❌ Dashboard file not found. Run tests first to generate results.")


if __name__ == '__main__':
    open_dashboard()
