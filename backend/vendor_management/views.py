import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from mongoengine.errors import ValidationError, DoesNotExist
from bson import ObjectId
from bson.errors import InvalidId

from accounts.models import User
from admin_controls.utils import check_admin_permission
from .models import Vendor

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_vendors(request):
    """List all vendors with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = Vendor.objects.all()

        # Apply search filter
        if search:
            queryset = queryset.filter(
                name__icontains=search
            )

        # Calculate pagination
        total_count = queryset.count()
        total_pages = (total_count + per_page - 1) // per_page
        skip = (page - 1) * per_page

        # Get vendors for current page
        vendors = queryset.skip(skip).limit(per_page)

        # Prepare vendor data with user details
        vendor_data = []
        for vendor in vendors:
            vendor_dict = vendor.to_dict()
            
            # Get user details for each vendor
            user_details = []
            for user_id in vendor.users:
                try:
                    user = User.objects.get(id=user_id)
                    user_details.append({
                        'id': str(user.id),
                        'email': user.email,
                        'full_name': user.full_name,
                        'user_type': user.user_type,
                        'is_active': user.is_active,
                    })
                except User.DoesNotExist:
                    continue
            
            vendor_dict['user_details'] = user_details
            vendor_data.append(vendor_dict)

        return Response({
            'success': True,
            'data': {
                'vendors': vendor_data,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'has_next': page < total_pages,
                    'has_previous': page > 1,
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_vendors: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch vendors'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_vendor(request):
    """Create a new vendor"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        name = data.get('name')
        users = data.get('users', [])
        primary_email = data.get('primary_email')
        primary_contact = data.get('primary_contact', '')
        website = data.get('website', '')
        description = data.get('description', '')

        if not name:
            return Response({
                'success': False,
                'error': 'Vendor name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not primary_email:
            return Response({
                'success': False,
                'error': 'Primary email is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate that all user IDs exist and are vendor type
        valid_users = []
        for user_id in users:
            try:
                user = User.objects.get(id=user_id, user_type='vendor')
                valid_users.append(str(user.id))
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'Invalid vendor user ID: {user_id}'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Create vendor
        vendor = Vendor(
            name=name,
            users=valid_users,
            primary_email=primary_email,
            primary_contact=primary_contact,
            website=website,
            description=description
        )
        vendor.save()

        logger.info(f"Vendor created: {vendor.name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Vendor created successfully',
            'vendor': vendor.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_vendor: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create vendor'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_vendor_details(request, vendor_id):
    """Get detailed information about a vendor"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            vendor = Vendor.objects.get(id=vendor_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Vendor not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Get user details
        user_details = []
        for user_id in vendor.users:
            try:
                user = User.objects.get(id=user_id)
                user_details.append({
                    'id': str(user.id),
                    'email': user.email,
                    'full_name': user.full_name,
                    'user_type': user.user_type,
                    'is_active': user.is_active,
                    'department': user.department,
                    'position': user.position,
                    'phone_number': user.phone_number,
                })
            except User.DoesNotExist:
                continue

        vendor_data = vendor.to_dict()
        vendor_data['user_details'] = user_details

        return Response({
            'success': True,
            'data': vendor_data
        })

    except Exception as e:
        logger.error(f"Error in get_vendor_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch vendor details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_vendor(request, vendor_id):
    """Update vendor information"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            vendor = Vendor.objects.get(id=vendor_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Vendor not found'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update allowed fields
        if 'name' in data:
            vendor.name = data['name']
        if 'primary_email' in data:
            vendor.primary_email = data['primary_email']
        if 'primary_contact' in data:
            vendor.primary_contact = data['primary_contact']
        if 'website' in data:
            vendor.website = data['website']
        if 'description' in data:
            vendor.description = data['description']

        # Handle users update
        if 'users' in data:
            users = data['users']
            valid_users = []
            for user_id in users:
                try:
                    user = User.objects.get(id=user_id, user_type='vendor')
                    valid_users.append(str(user.id))
                except User.DoesNotExist:
                    return Response({
                        'success': False,
                        'error': f'Invalid vendor user ID: {user_id}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            vendor.users = valid_users

        vendor.save()

        logger.info(f"Vendor updated: {vendor.name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Vendor updated successfully',
            'vendor': vendor.to_dict()
        })

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_vendor: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update vendor'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def delete_vendor(request, vendor_id):
    """Delete a vendor"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            vendor = Vendor.objects.get(id=vendor_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Vendor not found'
            }, status=status.HTTP_404_NOT_FOUND)

        vendor_name = vendor.name
        vendor.delete()

        logger.info(f"Vendor deleted: {vendor_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Vendor {vendor_name} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error in delete_vendor: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to delete vendor'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_vendor_users(request):
    """Get all users with vendor type for vendor assignment"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get all vendor type users
        vendor_users = User.objects.filter(user_type='vendor', is_active=True)
        
        user_data = []
        for user in vendor_users:
            user_data.append({
                'id': str(user.id),
                'email': user.email,
                'full_name': user.full_name,
                'employee_id': user.employee_id,
                'department': user.department,
                'position': user.position,
            })

        return Response({
            'success': True,
            'data': user_data
        })

    except Exception as e:
        logger.error(f"Error in get_vendor_users: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch vendor users'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
