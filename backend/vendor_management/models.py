from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON>ield, DateT<PERSON><PERSON><PERSON>
import datetime
from django.conf import settings

class Vendor(Document):
    """
    Vendor profile model stored in MongoDB
    """
    name = StringField(required=True, max_length=100)
    users = ListField(StringField())  # List of user IDs (UUID strings)
    primary_email = StringField(required=True, max_length=100)
    primary_contact = StringField(max_length=20)
    website = StringField(max_length=200)
    description = StringField(max_length=1000)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    
    meta = {
        'collection': 'vendor',
        'indexes': [
            'name',
            'primary_email',
            {'fields': ['$name', '$primary_email', '$description'],
             'default_language': 'english',
             'weights': {'name': 10, 'primary_email': 5, 'description': 1}
            }
        ]
    }
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Vendor, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert vendor to dictionary for API responses"""
        return {
            'id': str(self.id),
            'name': self.name,
            'users': self.users,
            'primary_email': self.primary_email,
            'primary_contact': self.primary_contact,
            'website': self.website,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
