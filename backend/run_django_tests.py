#!/usr/bin/env python3
"""
Django test runner with custom settings and reporting
"""
import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner
import json
from datetime import datetime


def run_tests():
    """Run Django tests with test settings"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.test_settings')
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
    
    # Run tests
    failures = test_runner.run_tests(['tests'])
    
    # Generate simple report
    results = {
        'timestamp': datetime.now().isoformat(),
        'status': 'passed' if failures == 0 else 'failed',
        'failures': failures,
    }
    
    # Save results
    os.makedirs('../test-results', exist_ok=True)
    with open('../test-results/backend-results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return failures


if __name__ == '__main__':
    failures = run_tests()
    sys.exit(failures)
