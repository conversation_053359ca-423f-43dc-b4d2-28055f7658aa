from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Custom User Admin"""

    list_display = ('email', 'employee_id', 'full_name', 'user_type', 'role_category', 'is_active', 'created_at')
    list_filter = ('user_type', 'role_category', 'is_active', 'is_staff', 'is_superuser', 'created_at')
    search_fields = ('email', 'employee_id', 'first_name', 'last_name', 'username')
    ordering = ('-created_at',)

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'employee_id', 'phone_number')}),
        (_('Role & Permissions'), {'fields': ('user_type', 'role_category', 'department', 'position')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
        (_('Metadata'), {'fields': ('created_by', 'last_login_ip')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'employee_id', 'password1', 'password2', 'user_type', 'role_category'),
        }),
    )

    readonly_fields = ('created_at', 'updated_at', 'last_login', 'date_joined')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        elif request.user.user_type == 'superadmin':
            return qs
        elif request.user.user_type == 'admin':
            return qs.exclude(user_type='superadmin')
        else:
            return qs.none()

    def has_change_permission(self, request, obj=None):
        if obj is None:
            return True
        if request.user.is_superuser:
            return True
        elif request.user.user_type == 'superadmin':
            return True
        elif request.user.user_type == 'admin':
            return obj.user_type != 'superadmin'
        return False

    def has_delete_permission(self, request, obj=None):
        if obj is None:
            return True
        if request.user.is_superuser:
            return True
        elif request.user.user_type == 'superadmin':
            return obj.user_type != 'superadmin'
        elif request.user.user_type == 'admin':
            return obj.user_type not in ['superadmin', 'admin']
        return False
