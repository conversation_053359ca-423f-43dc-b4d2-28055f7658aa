from django.urls import path
from . import views

app_name = 'job_management'

urlpatterns = [
    # Job role management
    path('job-roles/', views.list_job_roles, name='list_job_roles'),
    path('job-roles/create/', views.create_job_role, name='create_job_role'),
    path('job-roles/<str:role_id>/', views.get_job_role_details, name='get_job_role_details'),
    path('job-roles/<str:role_id>/update/', views.update_job_role, name='update_job_role'),
    path('job-roles/<str:role_id>/delete/', views.delete_job_role, name='delete_job_role'),
    
    # Job management
    path('jobs/', views.list_jobs, name='list_jobs'),
    path('jobs/create/', views.create_job, name='create_job'),
]
