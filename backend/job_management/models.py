from mongoengine import Document, StringField, IntField, DateTimeField, FileField, BooleanField
import datetime

class JobRole(Document):
    """
    Job Role model stored in MongoDB
    """
    role_name = StringField(required=True, max_length=100)
    years_of_experience = IntField(required=True, min_value=0)
    job_description = StringField(max_length=5000)
    job_description_file = FileField()  # For uploaded files (PDF, DOC, TXT)
    description_type = StringField(choices=['text', 'file'], default='text')  # Track input type
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    is_active = BooleanField(default=True)
    
    meta = {
        'collection': 'job_roles',
        'indexes': [
            'role_name',
            'years_of_experience',
            'is_active',
            {'fields': ['$role_name', '$job_description'],
             'default_language': 'english',
             'weights': {'role_name': 10, 'job_description': 5}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.role_name} ({self.years_of_experience} years)"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(JobRole, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job role to dictionary for API responses"""
        return {
            'id': str(self.id),
            'role_name': self.role_name,
            'years_of_experience': self.years_of_experience,
            'job_description': self.job_description,
            'description_type': self.description_type,
            'has_file': bool(self.job_description_file),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }

class Job(Document):
    """
    Job model stored in MongoDB
    """
    ticket_id = StringField(max_length=50)
    job_title = StringField(required=True, max_length=200)
    job_role_id = StringField(required=True)  # Reference to JobRole
    date = StringField(max_length=20)  # Date as string
    recruiter = StringField(max_length=100)
    ta_incharge = StringField(max_length=100)
    client = StringField(max_length=100)
    interview_panel = StringField(max_length=500)
    sourcing_type = StringField(choices=['vendor', 'direct', 'internal', 'all'])
    priority = StringField(choices=['high', 'medium', 'low'])
    candidates_applied = IntField(default=0)
    candidates_interviewed = IntField(default=0)
    availability = StringField(choices=['vacant', 'hired'])
    is_active = BooleanField(default=True)
    is_published = BooleanField(default=False)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the job
    
    meta = {
        'collection': 'jobs',
        'indexes': [
            'ticket_id',
            'job_title',
            'job_role_id',
            'recruiter',
            'client',
            'sourcing_type',
            'priority',
            'availability',
            'is_active',
            'is_published',
            'created_at',
            {'fields': ['$job_title', '$client', '$recruiter'],
             'default_language': 'english',
             'weights': {'job_title': 10, 'client': 5, 'recruiter': 3}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.job_title} at {self.company_name}"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Job, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job to dictionary for API responses"""
        return {
            'id': str(self.id),
            'ticket_id': self.ticket_id,
            'job_title': self.job_title,
            'job_role_id': self.job_role_id,
            'date': self.date,
            'recruiter': self.recruiter,
            'ta_incharge': self.ta_incharge,
            'client': self.client,
            'interview_panel': self.interview_panel,
            'sourcing_type': self.sourcing_type,
            'priority': self.priority,
            'candidates_applied': self.candidates_applied,
            'candidates_interviewed': self.candidates_interviewed,
            'availability': self.availability,
            'is_active': self.is_active,
            'is_published': self.is_published,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
        }
