from mongoengine import Document, StringField, IntField, DateTimeField, FileField, BooleanField
import datetime

class JobRole(Document):
    """
    Job Role model stored in MongoDB
    """
    role_name = StringField(required=True, max_length=100)
    years_of_experience = IntField(required=True, min_value=0)
    job_description = StringField(max_length=5000)
    job_description_file = FileField()  # For uploaded files (PDF, DOC, TXT)
    description_type = StringField(choices=['text', 'file'], default='text')  # Track input type
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    is_active = BooleanField(default=True)
    
    meta = {
        'collection': 'job_roles',
        'indexes': [
            'role_name',
            'years_of_experience',
            'is_active',
            {'fields': ['$role_name', '$job_description'],
             'default_language': 'english',
             'weights': {'role_name': 10, 'job_description': 5}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.role_name} ({self.years_of_experience} years)"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(JobRole, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job role to dictionary for API responses"""
        return {
            'id': str(self.id),
            'role_name': self.role_name,
            'years_of_experience': self.years_of_experience,
            'job_description': self.job_description,
            'description_type': self.description_type,
            'has_file': bool(self.job_description_file),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }

class Job(Document):
    """
    Job model stored in MongoDB
    """
    job_title = StringField(required=True, max_length=200)
    job_role_id = StringField(required=True)  # Reference to JobRole
    company_name = StringField(max_length=100)
    location = StringField(max_length=100)
    employment_type = StringField(choices=['full-time', 'part-time', 'contract', 'internship'], default='full-time')
    salary_range = StringField(max_length=100)
    job_description = StringField(max_length=5000)
    requirements = StringField(max_length=3000)
    benefits = StringField(max_length=2000)
    application_deadline = DateTimeField()
    is_active = BooleanField(default=True)
    is_published = BooleanField(default=False)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the job
    
    meta = {
        'collection': 'jobs',
        'indexes': [
            'job_title',
            'job_role_id',
            'company_name',
            'location',
            'employment_type',
            'is_active',
            'is_published',
            'application_deadline',
            {'fields': ['$job_title', '$job_description', '$requirements'],
             'default_language': 'english',
             'weights': {'job_title': 10, 'job_description': 5, 'requirements': 3}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.job_title} at {self.company_name}"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Job, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job to dictionary for API responses"""
        return {
            'id': str(self.id),
            'job_title': self.job_title,
            'job_role_id': self.job_role_id,
            'company_name': self.company_name,
            'location': self.location,
            'employment_type': self.employment_type,
            'salary_range': self.salary_range,
            'job_description': self.job_description,
            'requirements': self.requirements,
            'benefits': self.benefits,
            'application_deadline': self.application_deadline.isoformat() if self.application_deadline else None,
            'is_active': self.is_active,
            'is_published': self.is_published,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
        }
