import json
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from mongoengine.errors import ValidationError, DoesNotExist
from bson import ObjectId
from bson.errors import InvalidId

from accounts.models import User
from admin_controls.utils import check_admin_permission
from .models import JobRole, Job

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_job_roles(request):
    """List all job roles with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = JobRole.objects.filter(is_active=True)

        # Apply search filter
        if search:
            queryset = queryset.filter(role_name__icontains=search)

        # Calculate pagination
        total_count = queryset.count()
        total_pages = (total_count + per_page - 1) // per_page
        skip = (page - 1) * per_page

        # Get job roles for current page
        job_roles = queryset.skip(skip).limit(per_page)

        # Prepare job role data
        job_role_data = [role.to_dict() for role in job_roles]

        return Response({
            'success': True,
            'data': {
                'job_roles': job_role_data,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'has_next': page < total_pages,
                    'has_previous': page > 1,
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_job_roles: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch job roles'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_job_role(request):
    """Create a new job role"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        role_name = data.get('role_name')
        years_of_experience = data.get('years_of_experience')
        job_description = data.get('job_description', '')
        description_type = data.get('description_type', 'text')

        if not role_name:
            return Response({
                'success': False,
                'error': 'Role name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if years_of_experience is None:
            return Response({
                'success': False,
                'error': 'Years of experience is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            years_of_experience = int(years_of_experience)
            if years_of_experience < 0:
                raise ValueError("Years of experience cannot be negative")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Years of experience must be a valid non-negative number'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if job role already exists
        if JobRole.objects.filter(role_name=role_name, is_active=True).first():
            return Response({
                'success': False,
                'error': 'Job role with this name already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create job role
        job_role = JobRole(
            role_name=role_name,
            years_of_experience=years_of_experience,
            job_description=job_description,
            description_type=description_type
        )
        job_role.save()

        logger.info(f"Job role created: {job_role.role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job role created successfully',
            'job_role': job_role.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_job_role_details(request, role_id):
    """Get detailed information about a job role"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': True,
            'data': job_role.to_dict()
        })

    except Exception as e:
        logger.error(f"Error in get_job_role_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch job role details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_job_role(request, role_id):
    """Update job role information"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update allowed fields
        if 'role_name' in data:
            # Check if another role with this name exists
            existing_role = JobRole.objects.filter(
                role_name=data['role_name'], 
                is_active=True
            ).exclude(id=role_id).first()
            
            if existing_role:
                return Response({
                    'success': False,
                    'error': 'Job role with this name already exists'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            job_role.role_name = data['role_name']

        if 'years_of_experience' in data:
            try:
                years = int(data['years_of_experience'])
                if years < 0:
                    raise ValueError("Years of experience cannot be negative")
                job_role.years_of_experience = years
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'error': 'Years of experience must be a valid non-negative number'
                }, status=status.HTTP_400_BAD_REQUEST)

        if 'job_description' in data:
            job_role.job_description = data['job_description']

        if 'description_type' in data:
            job_role.description_type = data['description_type']

        job_role.save()

        logger.info(f"Job role updated: {job_role.role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job role updated successfully',
            'job_role': job_role.to_dict()
        })

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def delete_job_role(request, role_id):
    """Delete a job role (soft delete)"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        role_name = job_role.role_name
        job_role.is_active = False
        job_role.save()

        logger.info(f"Job role deleted: {role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Job role {role_name} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error in delete_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to delete job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_jobs(request):
    """List all jobs with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = Job.objects.filter(is_active=True)

        # Apply search filter
        if search:
            queryset = queryset.filter(job_title__icontains=search)

        # Calculate pagination
        total_count = queryset.count()
        total_pages = (total_count + per_page - 1) // per_page
        skip = (page - 1) * per_page

        # Get jobs for current page
        jobs = queryset.skip(skip).limit(per_page)

        # Prepare job data with role information
        job_data = []
        for job in jobs:
            job_dict = job.to_dict()
            
            # Get job role details
            try:
                job_role = JobRole.objects.get(id=job.job_role_id, is_active=True)
                job_dict['job_role'] = {
                    'id': str(job_role.id),
                    'role_name': job_role.role_name,
                    'years_of_experience': job_role.years_of_experience
                }
            except DoesNotExist:
                job_dict['job_role'] = None
            
            job_data.append(job_dict)

        return Response({
            'success': True,
            'data': {
                'jobs': job_data,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'has_next': page < total_pages,
                    'has_previous': page > 1,
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_jobs: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch jobs'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_job(request):
    """Create a new job"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        job_role_id = data.get('job_role_id')
        ticket_id = data.get('ticket_id')
        date = data.get('date')
        recruiter = data.get('recruiter')
        ta_incharge = data.get('ta_incharge')
        client = data.get('client')
        interview_panel = data.get('interview_panel')
        sourcing_type = data.get('sourcing_type')
        priority = data.get('priority')
        candidates_applied = data.get('candidates_applied', 0)
        candidates_interviewed = data.get('candidates_interviewed', 0)
        availability = data.get('availability')
        vendor_id = data.get('vendor_id')

        if not job_role_id:
            return Response({
                'success': False,
                'error': 'Job role is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify job role exists
        try:
            job_role = JobRole.objects.get(id=job_role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Invalid job role selected'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create job
        job = Job(
            ticket_id=ticket_id,
            job_role_id=job_role_id,
            job_title=job_role.role_name,  # Use role name as job title
            date=date,
            recruiter=recruiter,
            ta_incharge=ta_incharge,
            client=client,
            interview_panel=interview_panel,
            sourcing_type=sourcing_type,
            priority=priority,
            candidates_applied=candidates_applied,
            candidates_interviewed=candidates_interviewed,
            availability=availability,
            vendor_id=vendor_id,
            created_by=str(request.user.id)
        )
        job.save()

        logger.info(f"Job created: {job.job_title} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job created successfully',
            'job': job.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_job: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create job'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
