from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth.models import Group, Permission
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from accounts.models import User
import json
import logging

logger = logging.getLogger(__name__)

def check_admin_permission(user):
    """Check if user has admin permissions"""
    return user.is_authenticated and user.is_admin

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_users(request):
    """List all users with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')
        user_type = request.GET.get('user_type', '')
        role_category = request.GET.get('role_category', '')
        is_active = request.GET.get('is_active', '')

        # Build query
        queryset = User.objects.all()

        # Filter based on current user permissions
        if request.user.user_type == 'admin':
            queryset = queryset.exclude(user_type='superadmin')

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(email__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(employee_id__icontains=search)
            )

        if user_type:
            queryset = queryset.filter(user_type=user_type)

        if role_category:
            queryset = queryset.filter(role_category=role_category)

        if is_active:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Paginate
        paginator = Paginator(queryset.order_by('-created_at'), per_page)
        page_obj = paginator.get_page(page)

        # Serialize users
        users_data = []
        for user in page_obj:
            users_data.append({
                'id': str(user.id),
                'email': user.email,
                'employee_id': user.employee_id,
                'full_name': user.full_name,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'user_type': user.user_type,
                'role_category': user.role_category,
                'department': user.department,
                'position': user.position,
                'phone_number': user.phone_number,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'can_deactivate': request.user.can_deactivate_user(user),
                'groups': [{'id': group.id, 'name': group.name} for group in user.groups.all()],
            })

        return Response({
            'success': True,
            'data': {
                'users': users_data,
                'pagination': {
                    'current_page': page_obj.number,
                    'total_pages': paginator.num_pages,
                    'total_count': paginator.count,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous(),
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_users: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch users'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_user(request):
    """Create a new user"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = json.loads(request.body)

        # Validate required fields
        required_fields = ['email', 'password', 'user_type', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return Response({
                    'success': False,
                    'error': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user can create this type of user
        target_user_type = data.get('user_type')
        if target_user_type == 'superadmin':
            return Response({
                'success': False,
                'error': 'Superadmin users can only be created via CLI'
            }, status=status.HTTP_403_FORBIDDEN)

        if target_user_type == 'admin' and not request.user.can_create_admins():
            return Response({
                'success': False,
                'error': 'Permission denied to create admin users'
            }, status=status.HTTP_403_FORBIDDEN)

        # Check if email already exists
        if User.objects.filter(email=data['email']).exists():
            return Response({
                'success': False,
                'error': 'User with this email already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if employee_id already exists (if provided)
        employee_id = data.get('employee_id')
        if employee_id and User.objects.filter(employee_id=employee_id).exists():
            return Response({
                'success': False,
                'error': 'User with this employee ID already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create user
        user = User.objects.create_user(
            username=data['email'],
            email=data['email'],
            password=data['password'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            user_type=target_user_type,
            role_category=data.get('role_category', 'employee'),
            employee_id=employee_id,
            department=data.get('department', ''),
            position=data.get('position', ''),
            phone_number=data.get('phone_number', ''),
            created_by=request.user,
        )

        # Add user to group if specified
        group_id = data.get('group_id')
        if group_id:
            try:
                group = Group.objects.get(id=group_id)
                user.groups.add(group)
            except Group.DoesNotExist:
                pass  # Ignore if group doesn't exist

        logger.info(f"User created: {user.email} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'User created successfully',
            'user': {
                'id': str(user.id),
                'email': user.email,
                'employee_id': user.employee_id,
                'full_name': user.full_name,
                'user_type': user.user_type,
                'role_category': user.role_category,
            }
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_user: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create user'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_user(request, user_id):
    """Update an existing user"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        user = get_object_or_404(User, id=user_id)

        # Check if current user can modify this user
        if not request.user.can_deactivate_user(user):
            return Response({
                'success': False,
                'error': 'Permission denied to modify this user'
            }, status=status.HTTP_403_FORBIDDEN)

        data = json.loads(request.body)

        # Update allowed fields
        allowed_fields = ['first_name', 'last_name', 'department', 'position', 'phone_number', 'role_category']
        for field in allowed_fields:
            if field in data:
                setattr(user, field, data[field])

        # Handle user_type change (with restrictions)
        if 'user_type' in data:
            new_user_type = data['user_type']
            if new_user_type == 'superadmin':
                return Response({
                    'success': False,
                    'error': 'Cannot change user type to superadmin'
                }, status=status.HTTP_403_FORBIDDEN)

            if new_user_type == 'admin' and not request.user.can_create_admins():
                return Response({
                    'success': False,
                    'error': 'Permission denied to create admin users'
                }, status=status.HTTP_403_FORBIDDEN)

            user.user_type = new_user_type

        # Handle employee_id change
        if 'employee_id' in data:
            new_employee_id = data['employee_id']
            if new_employee_id and User.objects.filter(employee_id=new_employee_id).exclude(id=user.id).exists():
                return Response({
                    'success': False,
                    'error': 'User with this employee ID already exists'
                }, status=status.HTTP_400_BAD_REQUEST)
            user.employee_id = new_employee_id

        user.save()

        logger.info(f"User updated: {user.email} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'User updated successfully',
            'user': {
                'id': str(user.id),
                'email': user.email,
                'employee_id': user.employee_id,
                'full_name': user.full_name,
                'user_type': user.user_type,
                'role_category': user.role_category,
                'department': user.department,
                'position': user.position,
                'phone_number': user.phone_number,
                'is_active': user.is_active,
            }
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_user: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update user'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def toggle_user_status(request, user_id):
    """Activate or deactivate a user"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        user = get_object_or_404(User, id=user_id)

        # Check if current user can modify this user
        if not request.user.can_deactivate_user(user):
            return Response({
                'success': False,
                'error': 'Permission denied to modify this user'
            }, status=status.HTTP_403_FORBIDDEN)

        # Toggle status
        user.is_active = not user.is_active
        user.save()

        action = 'activated' if user.is_active else 'deactivated'
        logger.info(f"User {action}: {user.email} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'User {action} successfully',
            'user': {
                'id': str(user.id),
                'email': user.email,
                'is_active': user.is_active,
            }
        })

    except Exception as e:
        logger.error(f"Error in toggle_user_status: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update user status'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def bulk_user_action(request):
    """Perform bulk actions on users (activate, deactivate, delete)"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        action = data.get('action')  # 'activate', 'deactivate', 'delete'

        if not user_ids or not action:
            return Response({
                'success': False,
                'error': 'User IDs and action are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if action not in ['activate', 'deactivate', 'delete']:
            return Response({
                'success': False,
                'error': 'Invalid action. Must be activate, deactivate, or delete'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get users and check permissions
        users = User.objects.filter(id__in=user_ids)
        processed_users = []
        failed_users = []

        for user in users:
            if not request.user.can_deactivate_user(user):
                failed_users.append({
                    'id': str(user.id),
                    'email': user.email,
                    'reason': 'Permission denied'
                })
                continue

            try:
                if action == 'activate':
                    user.is_active = True
                    user.save()
                    processed_users.append({
                        'id': str(user.id),
                        'email': user.email,
                        'action': 'activated'
                    })
                elif action == 'deactivate':
                    user.is_active = False
                    user.save()
                    processed_users.append({
                        'id': str(user.id),
                        'email': user.email,
                        'action': 'deactivated'
                    })
                elif action == 'delete':
                    # Actually delete the user from the system
                    user_email = user.email
                    user_id = str(user.id)
                    user.delete()
                    processed_users.append({
                        'id': user_id,
                        'email': user_email,
                        'action': 'deleted'
                    })
            except Exception as e:
                failed_users.append({
                    'id': str(user.id),
                    'email': user.email,
                    'reason': str(e)
                })

        logger.info(f"Bulk action {action} performed by {request.user.email}: {len(processed_users)} successful, {len(failed_users)} failed")

        return Response({
            'success': True,
            'message': f'Bulk {action} completed',
            'processed': processed_users,
            'failed': failed_users
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in bulk_user_action: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to perform bulk action'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_details(request, user_id):
    """Get detailed information about a specific user"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        user = get_object_or_404(User, id=user_id)

        # Check if current user can view this user
        if request.user.user_type == 'admin' and user.user_type == 'superadmin':
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        return Response({
            'success': True,
            'user': {
                'id': str(user.id),
                'email': user.email,
                'username': user.username,
                'employee_id': user.employee_id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': user.full_name,
                'user_type': user.user_type,
                'role_category': user.role_category,
                'department': user.department,
                'position': user.position,
                'phone_number': user.phone_number,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat(),
                'updated_at': user.updated_at.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_by': {
                    'id': str(user.created_by.id),
                    'email': user.created_by.email,
                    'full_name': user.created_by.full_name,
                } if user.created_by else None,
                'can_deactivate': request.user.can_deactivate_user(user),
            }
        })

    except Exception as e:
        logger.error(f"Error in get_user_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch user details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Group Management Views

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_groups(request):
    """List all groups with pagination"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = Group.objects.all()

        # Apply search filter
        if search:
            queryset = queryset.filter(name__icontains=search)

        # Paginate
        paginator = Paginator(queryset.order_by('name'), per_page)
        page_obj = paginator.get_page(page)

        # Serialize groups
        groups_data = []
        for group in page_obj:
            groups_data.append({
                'id': group.id,
                'name': group.name,
                'permissions_count': group.permissions.count(),
                'users_count': group.user_set.count(),
            })

        return Response({
            'success': True,
            'data': {
                'groups': groups_data,
                'pagination': {
                    'current_page': page_obj.number,
                    'total_pages': paginator.num_pages,
                    'total_count': paginator.count,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous(),
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_groups: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch groups'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_group(request):
    """Create a new group"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        name = data.get('name')

        if not name:
            return Response({
                'success': False,
                'error': 'Group name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if group already exists
        if Group.objects.filter(name=name).exists():
            return Response({
                'success': False,
                'error': 'Group with this name already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create group
        group = Group.objects.create(name=name)

        logger.info(f"Group created: {group.name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Group created successfully',
            'group': {
                'id': group.id,
                'name': group.name,
                'permissions_count': 0,
                'users_count': 0,
            }
        })

    except Exception as e:
        logger.error(f"Error in create_group: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create group'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_group_details(request, group_id):
    """Get detailed information about a group including members and permissions"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        group = get_object_or_404(Group, id=group_id)

        # Get group members
        members = group.user_set.all()
        member_data = []
        for member in members:
            member_data.append({
                'id': str(member.id),
                'email': member.email,
                'full_name': member.full_name,
                'user_type': member.user_type,
                'is_active': member.is_active,
            })

        # Get group permissions
        permissions = group.permissions.all()
        permission_data = []
        for perm in permissions:
            permission_data.append({
                'id': perm.id,
                'name': perm.name,
                'codename': perm.codename,
                'content_type': perm.content_type.model,
            })

        return Response({
            'success': True,
            'group': {
                'id': group.id,
                'name': group.name,
                'members': member_data,
                'permissions': permission_data,
                'members_count': len(member_data),
                'permissions_count': len(permission_data),
            }
        })

    except Exception as e:
        logger.error(f"Error in get_group_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get group details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def add_user_to_group(request, group_id):
    """Add a user to a group"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        group = get_object_or_404(Group, id=group_id)
        data = json.loads(request.body)
        user_id = data.get('user_id')

        if not user_id:
            return Response({
                'success': False,
                'error': 'User ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        user = get_object_or_404(User, id=user_id)

        # Check if user can be managed by current user
        if not request.user.can_deactivate_user(user):
            return Response({
                'success': False,
                'error': 'Permission denied to manage this user'
            }, status=status.HTTP_403_FORBIDDEN)

        # Add user to group
        group.user_set.add(user)

        logger.info(f"User {user.email} added to group {group.name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'User {user.full_name} added to group {group.name}'
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in add_user_to_group: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to add user to group'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def remove_user_from_group(request, group_id):
    """Remove a user from a group"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        group = get_object_or_404(Group, id=group_id)
        data = json.loads(request.body)
        user_id = data.get('user_id')

        if not user_id:
            return Response({
                'success': False,
                'error': 'User ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        user = get_object_or_404(User, id=user_id)

        # Check if user can be managed by current user
        if not request.user.can_deactivate_user(user):
            return Response({
                'success': False,
                'error': 'Permission denied to manage this user'
            }, status=status.HTTP_403_FORBIDDEN)

        # Remove user from group
        group.user_set.remove(user)

        logger.info(f"User {user.email} removed from group {group.name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'User {user.full_name} removed from group {group.name}'
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in remove_user_from_group: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to remove user from group'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_group(request, group_id):
    """Update group information"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        group = get_object_or_404(Group, id=group_id)
        data = json.loads(request.body)

        # Update group name
        if 'name' in data:
            new_name = data['name'].strip()
            if not new_name:
                return Response({
                    'success': False,
                    'error': 'Group name cannot be empty'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if another group with this name exists
            if Group.objects.filter(name=new_name).exclude(id=group_id).exists():
                return Response({
                    'success': False,
                    'error': 'Group with this name already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            old_name = group.name
            group.name = new_name
            group.save()

            logger.info(f"Group renamed from {old_name} to {new_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Group updated successfully',
            'group': {
                'id': group.id,
                'name': group.name,
            }
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_group: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update group'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def reset_user_password(request, user_id):
    """Reset a user's password"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        user = get_object_or_404(User, id=user_id)

        # Check if current user can modify this user
        if not request.user.can_deactivate_user(user):
            return Response({
                'success': False,
                'error': 'Permission denied to modify this user'
            }, status=status.HTTP_403_FORBIDDEN)

        data = json.loads(request.body)
        new_password = data.get('new_password')

        if not new_password:
            return Response({
                'success': False,
                'error': 'New password is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if len(new_password) < 8:
            return Response({
                'success': False,
                'error': 'Password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user.set_password(new_password)
        user.save()

        logger.info(f"Password reset for user {user.email} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Password reset successfully for {user.full_name}'
        })

    except json.JSONDecodeError:
        return Response({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in reset_user_password: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to reset password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
