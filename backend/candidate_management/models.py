from mongoengine import Document, <PERSON>Field, IntField, DateTimeField, FileField, BooleanField, ListField, EmbeddedDocument, EmbeddedDocumentField
import datetime

class InterviewRound(EmbeddedDocument):
    """
    Interview Round embedded document
    """
    round_name = StringField(required=True, max_length=50)  # L1 Round, L2 Round, etc.
    status = StringField(choices=['scheduled', 'attended', 'hold', 'rejected', 'selected', 'dropped-out'])
    schedule_date = StringField(max_length=20)  # Date as string
    schedule_time = StringField(max_length=20)  # Time as string
    panel_name = StringField(max_length=200)
    panel_comment_score = StringField(max_length=1000)
    
    def to_dict(self):
        return {
            'round_name': self.round_name,
            'status': self.status,
            'schedule_date': self.schedule_date,
            'schedule_time': self.schedule_time,
            'panel_name': self.panel_name,
            'panel_comment_score': self.panel_comment_score,
        }

class Candidate(Document):
    """
    Candidate model stored in MongoDB
    """
    full_name = String<PERSON>ield(required=True, max_length=200)
    phone_number = StringField(max_length=20)
    email = StringField(max_length=200)
    candidate_id = StringField(max_length=50)
    preferred_role = StringField()  # Reference to JobRole ID
    optional_roles = ListField(StringField())  # List of JobRole IDs
    total_experience = StringField(max_length=50)
    last_job_date = StringField(max_length=20)  # Date as string
    resume_file = FileField()  # For uploaded resume files (PDF, DOC)
    comments = StringField(max_length=2000)
    
    # Interview rounds
    interview_rounds = ListField(EmbeddedDocumentField(InterviewRound))
    
    # Metadata
    is_active = BooleanField(default=True)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the candidate
    
    meta = {
        'collection': 'candidates',
        'indexes': [
            'full_name',
            'email',
            'phone_number',
            'candidate_id',
            'preferred_role',
            'is_active',
            'created_at'
        ]
    }
    
    def __str__(self):
        return f"{self.full_name} ({self.email})"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Candidate, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert candidate to dictionary for API responses"""
        return {
            'id': str(self.id),
            'full_name': self.full_name,
            'phone_number': self.phone_number,
            'email': self.email,
            'candidate_id': self.candidate_id,
            'preferred_role': self.preferred_role,
            'optional_roles': self.optional_roles,
            'total_experience': self.total_experience,
            'last_job_date': self.last_job_date,
            'has_resume': bool(self.resume_file),
            'comments': self.comments,
            'interview_rounds': [round.to_dict() for round in self.interview_rounds],
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
        }
