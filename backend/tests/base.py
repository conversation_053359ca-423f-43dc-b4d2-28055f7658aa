"""
Base test classes and utilities for Talent Hero backend tests
"""
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.urls import reverse
import json

User = get_user_model()


class BaseTestCase(TestCase):
    """Base test case with common setup and utilities"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test users
        self.superuser = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Super',
            last_name='User',
            user_type='superuser',
            is_superuser=True,
            is_staff=True,
        )
        
        self.admin_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User',
            user_type='admin',
            created_by=self.superuser,
        )
        
        self.regular_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Regular',
            last_name='User',
            user_type='user',
            created_by=self.admin_user,
        )
        
        self.vendor_user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Vendor',
            last_name='User',
            user_type='vendor',
        )
        
        # Create test groups
        self.test_group = Group.objects.create(name='Test Group')
        self.admin_group = Group.objects.create(name='Admin Group')
        
        # Add users to groups
        self.regular_user.groups.add(self.test_group)
        self.admin_user.groups.add(self.admin_group)
    
    def login_user(self, user):
        """Helper method to log in a user"""
        self.client.force_login(user)
    
    def post_json(self, url, data, **kwargs):
        """Helper method to post JSON data"""
        return self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json',
            **kwargs
        )
    
    def get_json_response(self, response):
        """Helper method to get JSON response data"""
        return json.loads(response.content.decode('utf-8'))
    
    def assertJsonResponse(self, response, expected_status=200):
        """Assert that response is JSON with expected status"""
        self.assertEqual(response.status_code, expected_status)
        self.assertEqual(response['Content-Type'], 'application/json')
        return self.get_json_response(response)
    
    def assertJsonSuccess(self, response, expected_status=200):
        """Assert that JSON response indicates success"""
        data = self.assertJsonResponse(response, expected_status)
        self.assertTrue(data.get('success', False))
        return data
    
    def assertJsonError(self, response, expected_status=400):
        """Assert that JSON response indicates error"""
        data = self.assertJsonResponse(response, expected_status)
        self.assertFalse(data.get('success', True))
        return data


class AuthTestMixin:
    """Mixin for authentication-related test utilities"""
    
    def generate_captcha(self):
        """Generate a captcha for testing"""
        response = self.client.post(reverse('generate_captcha'))
        data = self.assertJsonSuccess(response)
        return data.get('answer')
    
    def login_with_credentials(self, email, password, login_type='bce'):
        """Login with email/password and captcha"""
        captcha_answer = self.generate_captcha()
        
        login_data = {
            'email': email,
            'password': password,
            'captcha_answer': captcha_answer,
            'login_type': login_type,
        }
        
        return self.post_json(reverse('login'), login_data)


class AdminTestMixin:
    """Mixin for admin-related test utilities"""
    
    def create_test_user_data(self, **overrides):
        """Create test user data"""
        data = {
            'email': '<EMAIL>',
            'password': 'newpass123',
            'first_name': 'New',
            'last_name': 'User',
            'user_type': 'user',
            'employee_id': 'EMP001',
            'department': 'IT',
            'position': 'Developer',
            'phone_number': '+1234567890',
        }
        data.update(overrides)
        return data
    
    def create_test_group_data(self, **overrides):
        """Create test group data"""
        data = {
            'name': 'New Test Group',
        }
        data.update(overrides)
        return data
