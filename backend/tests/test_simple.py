"""
Simple tests to verify the testing framework works
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from .base import BaseTestCase

User = get_user_model()


class SimpleTestCase(TestCase):
    """Simple tests that should pass"""
    
    def test_basic_math(self):
        """Test basic math operations"""
        self.assertEqual(2 + 2, 4)
        self.assertEqual(10 - 5, 5)
        self.assertEqual(3 * 4, 12)
        self.assertEqual(8 / 2, 4)
    
    def test_string_operations(self):
        """Test string operations"""
        text = "Hello World"
        self.assertEqual(text.lower(), "hello world")
        self.assertEqual(text.upper(), "HELLO WORLD")
        self.assertTrue(text.startswith("Hello"))
        self.assertTrue(text.endswith("World"))
    
    def test_list_operations(self):
        """Test list operations"""
        items = [1, 2, 3, 4, 5]
        self.assertEqual(len(items), 5)
        self.assertIn(3, items)
        self.assertNotIn(6, items)
        self.assertEqual(items[0], 1)
        self.assertEqual(items[-1], 5)
    
    def test_dictionary_operations(self):
        """Test dictionary operations"""
        data = {'name': 'test', 'value': 42, 'active': True}
        self.assertIn('name', data)
        self.assertEqual(data['name'], 'test')
        self.assertEqual(data.get('value'), 42)
        self.assertTrue(data.get('active'))
        self.assertIsNone(data.get('missing'))


class DatabaseTestCase(BaseTestCase):
    """Simple database tests"""
    
    def test_user_creation(self):
        """Test that we can create users"""
        user_count_before = User.objects.count()
        
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_count_after = User.objects.count()
        
        self.assertEqual(user_count_after, user_count_before + 1)
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_query(self):
        """Test that we can query users"""
        # Use the users created in BaseTestCase
        all_users = User.objects.all()
        self.assertGreaterEqual(all_users.count(), 4)  # At least the 4 test users
        
        admin_users = User.objects.filter(user_type='admin')
        self.assertGreaterEqual(admin_users.count(), 1)
        
        active_users = User.objects.filter(is_active=True)
        self.assertGreaterEqual(active_users.count(), 1)
    
    def test_user_relationships(self):
        """Test user relationships work"""
        # Test that regular_user was created by admin_user
        self.assertEqual(self.regular_user.created_by, self.admin_user)
        
        # Test that admin_user was created by superuser
        self.assertEqual(self.admin_user.created_by, self.superuser)
        
        # Test group relationships
        self.assertIn(self.test_group, self.regular_user.groups.all())
        self.assertIn(self.admin_group, self.admin_user.groups.all())
