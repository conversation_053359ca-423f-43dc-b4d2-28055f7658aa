"""
Tests for admin controls functionality
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from .base import BaseTestCase, AdminTestMixin

User = get_user_model()


class UserManagementTestCase(BaseTestCase, AdminTestMixin):
    """Test user management functionality"""
    
    def test_list_users_as_admin(self):
        """Test listing users as admin"""
        self.login_user(self.admin_user)
        
        response = self.client.get(reverse('list_users'))
        data = self.assertJsonSuccess(response)
        
        self.assertIn('users', data['data'])
        self.assertIn('pagination', data['data'])
        
        users = data['data']['users']
        self.assertGreater(len(users), 0)
        
        # Check user data structure
        user = users[0]
        required_fields = ['id', 'email', 'full_name', 'user_type', 'is_active']
        for field in required_fields:
            self.assertIn(field, user)
    
    def test_list_users_unauthorized(self):
        """Test listing users without admin permission"""
        self.login_user(self.regular_user)
        
        response = self.client.get(reverse('list_users'))
        self.assertJsonError(response, 403)
    
    def test_create_user_as_admin(self):
        """Test creating user as admin"""
        self.login_user(self.admin_user)
        
        user_data = self.create_test_user_data()
        response = self.post_json(reverse('create_user'), user_data)
        data = self.assertJsonSuccess(response, 201)
        
        # Check that user was created
        self.assertTrue(User.objects.filter(email=user_data['email']).exists())
        
        # Check response data
        self.assertIn('user', data)
        self.assertEqual(data['user']['email'], user_data['email'])
    
    def test_create_user_with_group(self):
        """Test creating user and assigning to group"""
        self.login_user(self.admin_user)
        
        user_data = self.create_test_user_data(group_id=self.test_group.id)
        response = self.post_json(reverse('create_user'), user_data)
        data = self.assertJsonSuccess(response, 201)
        
        # Check that user was added to group
        user = User.objects.get(email=user_data['email'])
        self.assertIn(self.test_group, user.groups.all())
    
    def test_create_user_duplicate_email(self):
        """Test creating user with duplicate email"""
        self.login_user(self.admin_user)
        
        user_data = self.create_test_user_data(email=self.regular_user.email)
        response = self.post_json(reverse('create_user'), user_data)
        self.assertJsonError(response, 400)
    
    def test_create_user_invalid_data(self):
        """Test creating user with invalid data"""
        self.login_user(self.admin_user)
        
        user_data = self.create_test_user_data(email='invalid-email')
        response = self.post_json(reverse('create_user'), user_data)
        self.assertJsonError(response, 400)
    
    def test_get_user_details(self):
        """Test getting user details"""
        self.login_user(self.admin_user)
        
        response = self.client.get(
            reverse('get_user_details', kwargs={'user_id': self.regular_user.id})
        )
        data = self.assertJsonSuccess(response)
        
        self.assertIn('user', data)
        user = data['user']
        self.assertEqual(user['email'], self.regular_user.email)
        self.assertIn('groups', user)
    
    def test_update_user(self):
        """Test updating user"""
        self.login_user(self.admin_user)
        
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'department': 'Updated Department',
        }
        
        response = self.post_json(
            reverse('update_user', kwargs={'user_id': self.regular_user.id}),
            update_data
        )
        data = self.assertJsonSuccess(response)
        
        # Check that user was updated
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.first_name, 'Updated')
        self.assertEqual(self.regular_user.department, 'Updated Department')
    
    def test_toggle_user_status(self):
        """Test toggling user status"""
        self.login_user(self.admin_user)
        
        original_status = self.regular_user.is_active
        
        response = self.client.post(
            reverse('toggle_user_status', kwargs={'user_id': self.regular_user.id})
        )
        data = self.assertJsonSuccess(response)
        
        # Check that status was toggled
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.is_active, not original_status)
    
    def test_bulk_user_action_activate(self):
        """Test bulk activate users"""
        self.login_user(self.admin_user)
        
        # Deactivate user first
        self.regular_user.is_active = False
        self.regular_user.save()
        
        bulk_data = {
            'user_ids': [str(self.regular_user.id)],
            'action': 'activate'
        }
        
        response = self.post_json(reverse('bulk_user_action'), bulk_data)
        data = self.assertJsonSuccess(response)
        
        # Check that user was activated
        self.regular_user.refresh_from_db()
        self.assertTrue(self.regular_user.is_active)
        
        self.assertIn('processed', data)
        self.assertEqual(len(data['processed']), 1)
    
    def test_bulk_user_action_delete(self):
        """Test bulk delete users"""
        self.login_user(self.admin_user)
        
        user_id = str(self.regular_user.id)
        bulk_data = {
            'user_ids': [user_id],
            'action': 'delete'
        }
        
        response = self.post_json(reverse('bulk_user_action'), bulk_data)
        data = self.assertJsonSuccess(response)
        
        # Check that user was deleted
        self.assertFalse(User.objects.filter(id=self.regular_user.id).exists())
        
        self.assertIn('processed', data)
        self.assertEqual(len(data['processed']), 1)


class GroupManagementTestCase(BaseTestCase, AdminTestMixin):
    """Test group management functionality"""
    
    def test_list_groups(self):
        """Test listing groups"""
        self.login_user(self.admin_user)
        
        response = self.client.get(reverse('list_groups'))
        data = self.assertJsonSuccess(response)
        
        self.assertIn('groups', data['data'])
        self.assertIn('pagination', data['data'])
        
        groups = data['data']['groups']
        self.assertGreater(len(groups), 0)
        
        # Check group data structure
        group = groups[0]
        required_fields = ['id', 'name', 'permissions_count', 'users_count']
        for field in required_fields:
            self.assertIn(field, group)
    
    def test_create_group(self):
        """Test creating group"""
        self.login_user(self.admin_user)
        
        group_data = self.create_test_group_data()
        response = self.post_json(reverse('create_group'), group_data)
        data = self.assertJsonSuccess(response, 201)
        
        # Check that group was created
        self.assertTrue(Group.objects.filter(name=group_data['name']).exists())
        
        self.assertIn('group', data)
        self.assertEqual(data['group']['name'], group_data['name'])
    
    def test_create_group_duplicate_name(self):
        """Test creating group with duplicate name"""
        self.login_user(self.admin_user)
        
        group_data = self.create_test_group_data(name=self.test_group.name)
        response = self.post_json(reverse('create_group'), group_data)
        self.assertJsonError(response, 400)
    
    def test_get_group_details(self):
        """Test getting group details"""
        self.login_user(self.admin_user)
        
        response = self.client.get(
            reverse('get_group_details', kwargs={'group_id': self.test_group.id})
        )
        data = self.assertJsonSuccess(response)
        
        self.assertIn('group', data)
        group = data['group']
        self.assertEqual(group['name'], self.test_group.name)
        self.assertIn('members', group)
        self.assertIn('permissions', group)
    
    def test_add_user_to_group(self):
        """Test adding user to group"""
        self.login_user(self.admin_user)
        
        # Remove user from group first
        self.regular_user.groups.remove(self.test_group)
        
        add_data = {'user_id': str(self.regular_user.id)}
        response = self.post_json(
            reverse('add_user_to_group', kwargs={'group_id': self.test_group.id}),
            add_data
        )
        data = self.assertJsonSuccess(response)
        
        # Check that user was added to group
        self.assertIn(self.test_group, self.regular_user.groups.all())
    
    def test_remove_user_from_group(self):
        """Test removing user from group"""
        self.login_user(self.admin_user)
        
        # Ensure user is in group
        self.regular_user.groups.add(self.test_group)
        
        remove_data = {'user_id': str(self.regular_user.id)}
        response = self.post_json(
            reverse('remove_user_from_group', kwargs={'group_id': self.test_group.id}),
            remove_data
        )
        data = self.assertJsonSuccess(response)
        
        # Check that user was removed from group
        self.assertNotIn(self.test_group, self.regular_user.groups.all())
    
    def test_group_permissions_unauthorized(self):
        """Test group operations without admin permission"""
        self.login_user(self.regular_user)
        
        # Test create group
        group_data = self.create_test_group_data()
        response = self.post_json(reverse('create_group'), group_data)
        self.assertJsonError(response, 403)
        
        # Test list groups
        response = self.client.get(reverse('list_groups'))
        self.assertJsonError(response, 403)


class PermissionTestCase(BaseTestCase):
    """Test permission system"""
    
    def test_superuser_permissions(self):
        """Test superuser has all permissions"""
        self.login_user(self.superuser)
        
        # Should be able to access all admin endpoints
        response = self.client.get(reverse('list_users'))
        self.assertJsonSuccess(response)
        
        response = self.client.get(reverse('list_groups'))
        self.assertJsonSuccess(response)
    
    def test_admin_permissions(self):
        """Test admin permissions"""
        self.login_user(self.admin_user)
        
        # Should be able to access admin endpoints
        response = self.client.get(reverse('list_users'))
        self.assertJsonSuccess(response)
        
        # Should be able to manage users they created
        response = self.client.post(
            reverse('toggle_user_status', kwargs={'user_id': self.regular_user.id})
        )
        self.assertJsonSuccess(response)
    
    def test_regular_user_restrictions(self):
        """Test regular user restrictions"""
        self.login_user(self.regular_user)
        
        # Should not be able to access admin endpoints
        response = self.client.get(reverse('list_users'))
        self.assertJsonError(response, 403)
        
        response = self.client.get(reverse('list_groups'))
        self.assertJsonError(response, 403)
    
    def test_hierarchy_permissions(self):
        """Test user hierarchy permissions"""
        # Admin cannot deactivate superuser
        self.login_user(self.admin_user)
        response = self.client.post(
            reverse('toggle_user_status', kwargs={'user_id': self.superuser.id})
        )
        self.assertJsonError(response, 403)
        
        # Regular user cannot deactivate admin
        self.login_user(self.regular_user)
        response = self.client.post(
            reverse('toggle_user_status', kwargs={'user_id': self.admin_user.id})
        )
        self.assertJsonError(response, 403)
