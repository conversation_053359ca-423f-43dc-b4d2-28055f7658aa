"""
Tests for authentication system
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from .base import BaseTestCase, AuthTestMixin

User = get_user_model()


class CaptchaTestCase(BaseTestCase):
    """Test captcha generation and validation"""
    
    def test_generate_captcha(self):
        """Test captcha generation"""
        response = self.client.post(reverse('generate_captcha'))
        data = self.assertJsonSuccess(response)
        
        self.assertIn('question', data)
        self.assertIn('answer', data)
        self.assertTrue(data['question'].endswith(' = ?'))
    
    def test_captcha_stored_in_session(self):
        """Test that captcha answer is stored in session"""
        response = self.client.post(reverse('generate_captcha'))
        data = self.assertJsonSuccess(response)
        
        self.assertEqual(self.client.session['captcha_answer'], data['answer'])
    
    def test_captcha_regeneration(self):
        """Test that new captcha replaces old one"""
        # Generate first captcha
        response1 = self.client.post(reverse('generate_captcha'))
        data1 = self.assertJsonSuccess(response1)
        
        # Generate second captcha
        response2 = self.client.post(reverse('generate_captcha'))
        data2 = self.assertJsonSuccess(response2)
        
        # Session should have the new answer
        self.assertEqual(self.client.session['captcha_answer'], data2['answer'])


class LoginTestCase(BaseTestCase, AuthTestMixin):
    """Test login functionality"""
    
    def test_successful_bce_login(self):
        """Test successful BCE login"""
        response = self.login_with_credentials(
            self.admin_user.email, 
            'testpass123', 
            'bce'
        )
        data = self.assertJsonSuccess(response)
        
        self.assertEqual(data['user']['email'], self.admin_user.email)
        self.assertEqual(data['user']['user_type'], 'admin')
        self.assertTrue(data['permissions']['can_access_admin'])
    
    def test_successful_vendor_login(self):
        """Test successful vendor login"""
        response = self.login_with_credentials(
            self.vendor_user.email, 
            'testpass123', 
            'vendor'
        )
        data = self.assertJsonSuccess(response)
        
        self.assertEqual(data['user']['email'], self.vendor_user.email)
        self.assertEqual(data['user']['user_type'], 'vendor')
    
    def test_invalid_credentials(self):
        """Test login with invalid credentials"""
        captcha_answer = self.generate_captcha()
        
        login_data = {
            'email': self.admin_user.email,
            'password': 'wrongpassword',
            'captcha_answer': captcha_answer,
            'login_type': 'bce',
        }
        
        response = self.post_json(reverse('login'), login_data)
        data = self.assertJsonError(response, 401)
        
        self.assertIn('Invalid credentials', data['error'])
    
    def test_invalid_captcha(self):
        """Test login with invalid captcha"""
        self.generate_captcha()  # Generate captcha but use wrong answer
        
        login_data = {
            'email': self.admin_user.email,
            'password': 'testpass123',
            'captcha_answer': 'wrong_answer',
            'login_type': 'bce',
        }
        
        response = self.post_json(reverse('login'), login_data)
        data = self.assertJsonError(response, 400)
        
        self.assertIn('Invalid captcha', data['error'])
    
    def test_inactive_user_login(self):
        """Test login with inactive user"""
        self.admin_user.is_active = False
        self.admin_user.save()
        
        response = self.login_with_credentials(
            self.admin_user.email, 
            'testpass123', 
            'bce'
        )
        data = self.assertJsonError(response, 401)
        
        self.assertIn('Account is inactive', data['error'])
    
    def test_wrong_login_type(self):
        """Test BCE user trying to login via vendor"""
        response = self.login_with_credentials(
            self.admin_user.email, 
            'testpass123', 
            'vendor'
        )
        data = self.assertJsonError(response, 403)
        
        self.assertIn('Access denied', data['error'])
    
    def test_missing_fields(self):
        """Test login with missing required fields"""
        response = self.post_json(reverse('login'), {})
        data = self.assertJsonError(response, 400)
        
        self.assertIn('Email and password are required', data['error'])
    
    def test_user_permissions_in_response(self):
        """Test that user permissions are included in login response"""
        response = self.login_with_credentials(
            self.superuser.email, 
            'testpass123', 
            'bce'
        )
        data = self.assertJsonSuccess(response)
        
        permissions = data['permissions']
        self.assertTrue(permissions['can_access_admin'])
        self.assertTrue(permissions['can_create_users'])
        self.assertTrue(permissions['can_manage_groups'])


class LogoutTestCase(BaseTestCase):
    """Test logout functionality"""
    
    def test_successful_logout(self):
        """Test successful logout"""
        self.login_user(self.admin_user)
        
        response = self.client.post(reverse('logout'))
        data = self.assertJsonSuccess(response)
        
        self.assertEqual(data['message'], 'Logged out successfully')
        
        # Check that session is cleared
        self.assertNotIn('_auth_user_id', self.client.session)
    
    def test_logout_without_login(self):
        """Test logout without being logged in"""
        response = self.client.post(reverse('logout'))
        data = self.assertJsonSuccess(response)
        
        self.assertEqual(data['message'], 'Logged out successfully')


class SessionTestCase(BaseTestCase):
    """Test session management"""
    
    def test_session_creation_on_login(self):
        """Test that session is created on login"""
        initial_session_count = Session.objects.count()
        
        self.login_user(self.admin_user)
        
        # Session should be created
        self.assertGreater(Session.objects.count(), initial_session_count)
    
    def test_session_cleanup_on_logout(self):
        """Test that session is cleaned up on logout"""
        self.login_user(self.admin_user)
        session_key = self.client.session.session_key
        
        response = self.client.post(reverse('logout'))
        self.assertJsonSuccess(response)
        
        # Session should be deleted
        self.assertFalse(Session.objects.filter(session_key=session_key).exists())


class UserModelTestCase(BaseTestCase):
    """Test custom user model functionality"""
    
    def test_user_creation(self):
        """Test user creation with custom fields"""
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            user_type='user',
            employee_id='EMP123',
            department='IT',
            position='Developer',
            phone_number='+1234567890',
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.full_name, 'Test User')
        self.assertEqual(user.employee_id, 'EMP123')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_permissions(self):
        """Test user permission methods"""
        # Superuser can deactivate anyone
        self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
        self.assertTrue(self.superuser.can_deactivate_user(self.regular_user))
        
        # Admin can deactivate users they created
        self.assertTrue(self.admin_user.can_deactivate_user(self.regular_user))
        
        # Admin cannot deactivate superuser
        self.assertFalse(self.admin_user.can_deactivate_user(self.superuser))
        
        # Regular user cannot deactivate anyone
        self.assertFalse(self.regular_user.can_deactivate_user(self.admin_user))
    
    def test_user_string_representation(self):
        """Test user string representation"""
        self.assertEqual(str(self.admin_user), '<EMAIL>')
    
    def test_full_name_property(self):
        """Test full_name property"""
        self.assertEqual(self.admin_user.full_name, 'Admin User')
        
        # Test with missing last name
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            user_type='user',
        )
        self.assertEqual(user.full_name, 'Test')
