"""
Tests for Django models
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.core.exceptions import ValidationError
from .base import BaseTestCase

User = get_user_model()


class UserModelTestCase(BaseTestCase):
    """Test User model functionality"""
    
    def test_user_creation_with_required_fields(self):
        """Test creating user with required fields only"""
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, '<EMAIL>')
        self.assertTrue(user.check_password('testpass123'))
        self.assertEqual(user.user_type, 'user')  # Default value
        self.assertTrue(user.is_active)
    
    def test_user_creation_with_all_fields(self):
        """Test creating user with all fields"""
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='Complete',
            last_name='User',
            user_type='admin',
            role_category='employee',
            employee_id='EMP123',
            department='IT',
            position='Developer',
            phone_number='+1234567890',
            created_by=self.superuser
        )
        
        self.assertEqual(user.full_name, 'Complete User')
        self.assertEqual(user.employee_id, 'EMP123')
        self.assertEqual(user.department, 'IT')
        self.assertEqual(user.position, 'Developer')
        self.assertEqual(user.phone_number, '+1234567890')
        self.assertEqual(user.created_by, self.superuser)
    
    def test_user_string_representation(self):
        """Test user __str__ method"""
        self.assertEqual(str(self.admin_user), '<EMAIL>')
    
    def test_full_name_property(self):
        """Test full_name property"""
        # With both first and last name
        self.assertEqual(self.admin_user.full_name, 'Admin User')
        
        # With only first name
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            first_name='FirstOnly'
        )
        self.assertEqual(user.full_name, 'FirstOnly')
        
        # With only last name
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            last_name='LastOnly'
        )
        self.assertEqual(user.full_name, 'LastOnly')
        
        # With neither
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertEqual(user.full_name, '<EMAIL>')
    
    def test_user_permissions_hierarchy(self):
        """Test user permission hierarchy"""
        # Superuser can deactivate everyone
        self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
        self.assertTrue(self.superuser.can_deactivate_user(self.regular_user))
        self.assertTrue(self.superuser.can_deactivate_user(self.vendor_user))
        
        # Admin can deactivate users they created
        self.assertTrue(self.admin_user.can_deactivate_user(self.regular_user))
        
        # Admin cannot deactivate superuser or other admins
        self.assertFalse(self.admin_user.can_deactivate_user(self.superuser))
        
        # Regular user cannot deactivate anyone
        self.assertFalse(self.regular_user.can_deactivate_user(self.admin_user))
        self.assertFalse(self.regular_user.can_deactivate_user(self.vendor_user))
        
        # Users cannot deactivate themselves
        self.assertFalse(self.regular_user.can_deactivate_user(self.regular_user))
    
    def test_user_groups(self):
        """Test user group relationships"""
        # User should be able to be added to groups
        self.regular_user.groups.add(self.test_group)
        self.assertIn(self.test_group, self.regular_user.groups.all())
        
        # User can be in multiple groups
        new_group = Group.objects.create(name='Another Group')
        self.regular_user.groups.add(new_group)
        self.assertEqual(self.regular_user.groups.count(), 2)
        
        # User can be removed from groups
        self.regular_user.groups.remove(self.test_group)
        self.assertNotIn(self.test_group, self.regular_user.groups.all())
    
    def test_user_type_choices(self):
        """Test user type validation"""
        valid_types = ['superuser', 'admin', 'user', 'vendor']
        
        for user_type in valid_types:
            user = User.objects.create_user(
                username=f'{user_type}@test.com',
                email=f'{user_type}@test.com',
                password='testpass123',
                user_type=user_type
            )
            self.assertEqual(user.user_type, user_type)
    
    def test_email_uniqueness(self):
        """Test that email addresses must be unique"""
        with self.assertRaises(Exception):
            User.objects.create_user(
                username='<EMAIL>',
                email=self.admin_user.email,  # Duplicate email
                password='testpass123'
            )
    
    def test_user_active_status(self):
        """Test user active status"""
        # New users should be active by default
        user = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123'
        )
        self.assertTrue(user.is_active)
        
        # Can deactivate user
        user.is_active = False
        user.save()
        user.refresh_from_db()
        self.assertFalse(user.is_active)


class GroupModelTestCase(BaseTestCase):
    """Test Group model functionality"""
    
    def test_group_creation(self):
        """Test creating groups"""
        group = Group.objects.create(name='Test Group 2')
        self.assertEqual(group.name, 'Test Group 2')
        self.assertEqual(str(group), 'Test Group 2')
    
    def test_group_user_relationship(self):
        """Test group-user many-to-many relationship"""
        # Add user to group
        self.test_group.user_set.add(self.regular_user)
        self.assertIn(self.regular_user, self.test_group.user_set.all())
        
        # Remove user from group
        self.test_group.user_set.remove(self.regular_user)
        self.assertNotIn(self.regular_user, self.test_group.user_set.all())
    
    def test_group_permissions(self):
        """Test group permissions"""
        # Groups can have permissions
        from django.contrib.auth.models import Permission
        from django.contrib.contenttypes.models import ContentType
        
        content_type = ContentType.objects.get_for_model(User)
        permission = Permission.objects.create(
            codename='test_permission',
            name='Test Permission',
            content_type=content_type
        )
        
        self.test_group.permissions.add(permission)
        self.assertIn(permission, self.test_group.permissions.all())
    
    def test_group_name_uniqueness(self):
        """Test that group names must be unique"""
        with self.assertRaises(Exception):
            Group.objects.create(name=self.test_group.name)
