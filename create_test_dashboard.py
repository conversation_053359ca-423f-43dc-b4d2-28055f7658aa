#!/usr/bin/env python3
"""
Create a unified test dashboard that persists results
"""
import json
import os
from datetime import datetime
from pathlib import Path


def create_unified_dashboard():
    """Create a unified HTML dashboard for all test results"""
    
    project_root = Path(__file__).parent
    test_results_dir = project_root / 'test-results'
    test_results_dir.mkdir(exist_ok=True)
    
    # Load latest test results
    latest_results = {}
    
    # Load comprehensive test results
    latest_file = test_results_dir / 'latest-test-report.json'
    if latest_file.exists():
        with open(latest_file, 'r') as f:
            latest_results = json.load(f)
    
    # Load unit test results
    unit_file = test_results_dir / 'latest-unit-test-report.json'
    unit_results = {}
    if unit_file.exists():
        with open(unit_file, 'r') as f:
            unit_results = json.load(f)
    
    # Load Playwright results
    playwright_file = test_results_dir / 'playwright-results.json'
    playwright_results = {}
    if playwright_file.exists():
        with open(playwright_file, 'r') as f:
            playwright_results = json.load(f)
    
    # Generate HTML dashboard
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talent Hero - Test Dashboard</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .dashboard {{
            max-width: 1400px;
            margin: 0 auto;
        }}
        
        .header {{
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }}
        
        .header h1 {{
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-size: 1.2rem;
            opacity: 0.9;
        }}
        
        .test-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .test-card {{
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }}
        
        .test-card h2 {{
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .status-badge {{
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }}
        
        .status-passed {{
            background: #d4edda;
            color: #155724;
        }}
        
        .status-failed {{
            background: #f8d7da;
            color: #721c24;
        }}
        
        .status-error {{
            background: #fff3cd;
            color: #856404;
        }}
        
        .test-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        
        .stat-item {{
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }}
        
        .stat-number {{
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }}
        
        .stat-label {{
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }}
        
        .timestamp {{
            color: #666;
            font-size: 0.9rem;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }}
        
        .no-data {{
            color: #666;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }}
        
        .refresh-btn {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            color: #667eea;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.2s;
        }}
        
        .refresh-btn:hover {{
            transform: translateY(-2px);
        }}
        
        .links {{
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }}
        
        .links h3 {{
            margin-bottom: 15px;
            color: #333;
        }}
        
        .links a {{
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.2s;
        }}
        
        .links a:hover {{
            background: #5a67d8;
        }}
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🧪 Test Dashboard</h1>
            <p>Talent Hero Testing Results</p>
        </div>
        
        <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        
        <div class="test-grid">
            {generate_test_card("Frontend Unit Tests", latest_results.get('frontend', {}), "🎨")}
            {generate_test_card("Backend Unit Tests", latest_results.get('backend', {}), "⚙️")}
            {generate_test_card("E2E Tests", latest_results.get('e2e', {}), "🎭")}
        </div>
        
        <div class="links">
            <h3>📊 Detailed Reports</h3>
            <a href="latest-test-report.html" target="_blank">Comprehensive Report</a>
            <a href="../playwright-report/index.html" target="_blank">E2E Report</a>
            <a href="latest-unit-test-report.json" target="_blank">Unit Test JSON</a>
            <a href="latest-test-report.json" target="_blank">Full Results JSON</a>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds if tests are running
        setInterval(() => {{
            fetch('latest-test-report.json')
                .then(response => response.json())
                .then(data => {{
                    const now = new Date();
                    const lastUpdate = new Date(data.timestamp);
                    const diffMinutes = (now - lastUpdate) / (1000 * 60);
                    
                    // If last update was less than 2 minutes ago, auto-refresh
                    if (diffMinutes < 2) {{
                        location.reload();
                    }}
                }})
                .catch(() => {{
                    // Ignore errors
                }});
        }}, 30000);
    </script>
</body>
</html>
    """
    
    # Save dashboard
    dashboard_file = test_results_dir / 'dashboard.html'
    with open(dashboard_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"📊 Test dashboard created: {dashboard_file}")
    print(f"🌐 Open in browser: file://{dashboard_file.absolute()}")
    
    return dashboard_file


def generate_test_card(title, results, icon):
    """Generate HTML for a test card"""
    if not results:
        return f"""
        <div class="test-card">
            <h2>{icon} {title}</h2>
            <div class="no-data">No test results available</div>
        </div>
        """
    
    status = results.get('status', 'unknown')
    status_class = f"status-{status}" if status in ['passed', 'failed', 'error'] else 'status-error'
    
    # Parse test statistics
    stdout = results.get('stdout', '')
    stats_html = ""
    
    if 'Test Files' in stdout and 'Tests' in stdout:
        # Frontend test parsing
        lines = stdout.split('\n')
        for line in lines:
            if 'Test Files' in line:
                stats_html += f"<div class='stat-item'><div class='stat-number'>📁</div><div class='stat-label'>{line.strip()}</div></div>"
            elif 'Tests' in line and ('passed' in line or 'failed' in line):
                stats_html += f"<div class='stat-item'><div class='stat-number'>🧪</div><div class='stat-label'>{line.strip()}</div></div>"
    elif 'Found' in stdout and 'test(s)' in stdout:
        # Backend test parsing
        lines = stdout.split('\n')
        for line in lines:
            if 'Found' in line and 'test(s)' in line:
                stats_html += f"<div class='stat-item'><div class='stat-number'>🔍</div><div class='stat-label'>{line.strip()}</div></div>"
            elif 'Tests:' in line and ('passed' in line or 'failed' in line):
                stats_html += f"<div class='stat-item'><div class='stat-number'>📊</div><div class='stat-label'>{line.strip()}</div></div>"
    
    if not stats_html:
        stats_html = f"<div class='stat-item'><div class='stat-number'>{status.upper()}</div><div class='stat-label'>Status</div></div>"
    
    return_code = results.get('return_code', 'N/A')
    
    return f"""
    <div class="test-card">
        <h2>
            {icon} {title}
            <span class="status-badge {status_class}">{status}</span>
        </h2>
        <div class="test-stats">
            {stats_html}
            <div class="stat-item">
                <div class="stat-number">{return_code}</div>
                <div class="stat-label">Exit Code</div>
            </div>
        </div>
        <div class="timestamp">
            Last run: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
    """


if __name__ == '__main__':
    create_unified_dashboard()
