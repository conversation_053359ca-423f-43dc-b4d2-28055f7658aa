
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talent Hero Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .summary {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .status-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .status-card p {
            margin: 0;
            font-size: 1.5em;
            text-transform: uppercase;
        }
        .details {
            padding: 0 30px 30px 30px;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            font-size: 1.1em;
        }
        .section-content {
            padding: 20px;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Talent Hero Test Report</h1>
            <p>Comprehensive Testing Results</p>
        </div>
        
        <div class="summary">
            <div class="status-card" style="background-color: #dc3545">
                <h3>Overall Status</h3>
                <p>FAILED</p>
            </div>
            <div class="status-card" style="background-color: #dc3545">
                <h3>Frontend Tests</h3>
                <p>FAILED</p>
            </div>
            <div class="status-card" style="background-color: #dc3545">
                <h3>Backend Tests</h3>
                <p>FAILED</p>
            </div>
        </div>
        
        <div class="details">
            <div class="section">
                <div class="section-header">🎨 Frontend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> failed</p>
                    <p><strong>Return Code:</strong> 1</p>
                    
                    
                    
                    
                    
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">⚙️ Backend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> failed</p>
                    <p><strong>Return Code:</strong> 1</p>
                    
                    
            <h4>Backend Output</h4>
            <div class="test-output">Found 54 test(s).
Operations to perform:
  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles
  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions
Synchronizing apps without migrations:
  Creating tables...
    Running deferred SQL...
Running migrations:
  No migrations to apply.
System check identified no issues (0 silenced).
</div>
        
                    
            <h4>Backend Errors</h4>
            <div class="error">Using existing test database for alias 'default' ('test_th')...

test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)
Test adding user to group ... ERROR
test_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)
Test creating group ... ERROR
test_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)
Test creating group with duplicate name ... ERROR
test_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)
Test getting group details ... ERROR
test_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)
Test group operations without admin permission ... ERROR
test_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)
Test listing groups ... ERROR
test_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)
Test removing user from group ... ERROR
test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)
Test admin permissions ... ERROR
test_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)
Test user hierarchy permissions ... ERROR
test_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)
Test regular user restrictions ... ERROR
test_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)
Test superuser has all permissions ... ERROR
test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)
Test bulk activate users ... ERROR
test_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)
Test bulk delete users ... ERROR
test_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)
Test creating user as admin ... ERROR
test_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)
Test creating user with duplicate email ... ERROR
test_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)
Test creating user with invalid data ... ERROR
test_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)
Test creating user and assigning to group ... ERROR
test_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)
Test getting user details ... ERROR
test_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)
Test listing users as admin ... ERROR
test_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)
Test listing users without admin permission ... ERROR
test_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)
Test toggling user status ... ERROR
test_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)
Test updating user ... ERROR
test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)
Test that new captcha replaces old one ... ERROR
test_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)
Test that captcha answer is stored in session ... ERROR
test_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)
Test captcha generation ... ERROR
test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)
Test login with inactive user ... ERROR
test_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)
Test login with invalid captcha ... ERROR
test_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)
Test login with invalid credentials ... ERROR
test_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)
Test login with missing required fields ... ERROR
test_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)
Test successful BCE login ... ERROR
test_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)
Test successful vendor login ... ERROR
test_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)
Test that user permissions are included in login response ... ERROR
test_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)
Test BCE user trying to login via vendor ... ERROR
test_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)
Test logout without being logged in ... ERROR
test_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)
Test successful logout ... ERROR
test_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)
Test that session is cleaned up on logout ... ERROR
test_session_creation_on_login (tests.test_auth.SessionTestCase.test_session_creation_on_login)
Test that session is created on login ... ok
test_full_name_property (tests.test_auth.UserModelTestCase.test_full_name_property)
Test full_name property ... ok
test_user_creation (tests.test_auth.UserModelTestCase.test_user_creation)
Test user creation with custom fields ... ok
test_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)
Test user permission methods ... FAIL
test_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)
Test user string representation ... FAIL
test_group_creation (tests.test_models.GroupModelTestCase.test_group_creation)
Test creating groups ... ok
test_group_name_uniqueness (tests.test_models.GroupModelTestCase.test_group_name_uniqueness)
Test that group names must be unique ... ok
test_group_permissions (tests.test_models.GroupModelTestCase.test_group_permissions)
Test group permissions ... ok
test_group_user_relationship (tests.test_models.GroupModelTestCase.test_group_user_relationship)
Test group-user many-to-many relationship ... ok
test_email_uniqueness (tests.test_models.UserModelTestCase.test_email_uniqueness)
Test that email addresses must be unique ... ok
test_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)
Test full_name property ... FAIL
test_user_active_status (tests.test_models.UserModelTestCase.test_user_active_status)
Test user active status ... ok
test_user_creation_with_all_fields (tests.test_models.UserModelTestCase.test_user_creation_with_all_fields)
Test creating user with all fields ... ok
test_user_creation_with_required_fields (tests.test_models.UserModelTestCase.test_user_creation_with_required_fields)
Test creating user with required fields only ... ok
test_user_groups (tests.test_models.UserModelTestCase.test_user_groups)
Test user group relationships ... ok
test_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)
Test user permission hierarchy ... FAIL
test_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)
Test user __str__ method ... FAIL
test_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)
Test user type validation ... ERROR

======================================================================
ERROR: test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)
Test adding user to group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 246, in test_add_user_to_group
    reverse('add_user_to_group', kwargs={'group_id': self.test_group.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'add_user_to_group' not found. 'add_user_to_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)
Test creating group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 205, in test_create_group
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)
Test creating group with duplicate name
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 219, in test_create_group_duplicate_name
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)
Test getting group details
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 227, in test_get_group_details
    reverse('get_group_details', kwargs={'group_id': self.test_group.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'get_group_details' not found. 'get_group_details' is not a valid view function or pattern name.

======================================================================
ERROR: test_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)
Test group operations without admin permission
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 277, in test_group_permissions_unauthorized
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)
Test listing groups
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 185, in test_list_groups
    response = self.client.get(reverse('list_groups'))
                               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_groups' not found. 'list_groups' is not a valid view function or pattern name.

======================================================================
ERROR: test_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)
Test removing user from group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 263, in test_remove_user_from_group
    reverse('remove_user_from_group', kwargs={'group_id': self.test_group.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'remove_user_from_group' not found. 'remove_user_from_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)
Test admin permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 304, in test_admin_permissions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)
Test user hierarchy permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 329, in test_hierarchy_permissions
    reverse('toggle_user_status', kwargs={'user_id': self.superuser.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.

======================================================================
ERROR: test_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)
Test regular user restrictions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 318, in test_regular_user_restrictions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)
Test superuser has all permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 293, in test_superuser_permissions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)
Test bulk activate users
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 148, in test_bulk_user_action_activate
    response = self.post_json(reverse('bulk_user_action'), bulk_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.

======================================================================
ERROR: test_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)
Test bulk delete users
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 168, in test_bulk_user_action_delete
    response = self.post_json(reverse('bulk_user_action'), bulk_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)
Test creating user as admin
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 47, in test_create_user_as_admin
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)
Test creating user with duplicate email
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 74, in test_create_user_duplicate_email
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)
Test creating user with invalid data
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 82, in test_create_user_invalid_data
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)
Test creating user and assigning to group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 62, in test_create_user_with_group
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)
Test getting user details
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 90, in test_get_user_details
    reverse('get_user_details', kwargs={'user_id': self.regular_user.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'get_user_details' not found. 'get_user_details' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)
Test listing users as admin
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 20, in test_list_users_as_admin
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)
Test listing users without admin permission
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 39, in test_list_users_unauthorized
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)
Test toggling user status
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 127, in test_toggle_user_status
    reverse('toggle_user_status', kwargs={'user_id': self.regular_user.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.

======================================================================
ERROR: test_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)
Test updating user
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 110, in test_update_user
    reverse('update_user', kwargs={'user_id': self.regular_user.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'update_user' not found. 'update_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)
Test that new captcha replaces old one
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 35, in test_captcha_regeneration
    response1 = self.client.post(reverse('generate_captcha'))
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)
Test that captcha answer is stored in session
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 27, in test_captcha_stored_in_session
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)
Test captcha generation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 18, in test_generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)
Test login with inactive user
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 111, in test_inactive_user_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)
Test login with invalid captcha
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 92, in test_invalid_captcha
    self.generate_captcha()  # Generate captcha but use wrong answer
    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)
Test login with invalid credentials
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 76, in test_invalid_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)
Test login with missing required fields
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 133, in test_missing_fields
    response = self.post_json(reverse('login'), {})
                              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'login' not found. 'login' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)
Test successful BCE login
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 51, in test_successful_bce_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)
Test successful vendor login
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 64, in test_successful_vendor_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)
Test that user permissions are included in login response
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 140, in test_user_permissions_in_response
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)
Test BCE user trying to login via vendor
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 122, in test_wrong_login_type
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)
Test logout without being logged in
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 170, in test_logout_without_login
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)
Test successful logout
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 160, in test_successful_logout
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)
Test that session is cleaned up on logout
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 193, in test_session_cleanup_on_logout
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)
Test user type validation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\psycopg\cursor.py", line 97, in execute
    raise ex.with_traceback(None)
psycopg.errors.UniqueViolation: duplicate key value violates unique constraint "accounts_user_username_key"
DETAIL:  Key (username)=(<EMAIL>) already exists.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 129, in test_user_type_choices
    user = User.objects.create_user(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\models.py", line 16, in create_user
    user.save(using=self._db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\contrib\auth\base_user.py", line 65, in save
    super().save(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\psycopg\cursor.py", line 97, in execute
    raise ex.with_traceback(None)
django.db.utils.IntegrityError: duplicate key value violates unique constraint "accounts_user_username_key"
DETAIL:  Key (username)=(<EMAIL>) already exists.

======================================================================
FAIL: test_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)
Test user permission methods
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 226, in test_user_permissions
    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
AssertionError: False is not true

======================================================================
FAIL: test_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)
Test user string representation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 240, in test_user_string_representation
    self.assertEqual(str(self.admin_user), '<EMAIL>')
AssertionError: '<EMAIL> (Admin)' != '<EMAIL>'
- <EMAIL> (Admin)
?               --------
+ <EMAIL>


======================================================================
FAIL: test_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)
Test full_name property
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 87, in test_full_name_property
    self.assertEqual(user.full_name, '<EMAIL>')
AssertionError: '' != '<EMAIL>'
+ <EMAIL>


======================================================================
FAIL: test_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)
Test user permission hierarchy
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 92, in test_user_permissions_hierarchy
    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
AssertionError: False is not true

======================================================================
FAIL: test_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)
Test user __str__ method
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 56, in test_user_string_representation
    self.assertEqual(str(self.admin_user), '<EMAIL>')
AssertionError: '<EMAIL> (Admin)' != '<EMAIL>'
- <EMAIL> (Admin)
?               --------
+ <EMAIL>


----------------------------------------------------------------------
Ran 54 tests in 62.949s

FAILED (failures=5, errors=37)
Preserving test database for alias 'default' ('test_th')...

</div>
        
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Generated on: 2025-07-15 00:30:31
        </div>
    </div>
</body>
</html>
        