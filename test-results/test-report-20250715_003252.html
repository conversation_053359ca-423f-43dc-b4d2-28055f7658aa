
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Talent Hero Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .summary {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .status-card {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        .status-card p {
            margin: 0;
            font-size: 1.5em;
            text-transform: uppercase;
        }
        .details {
            padding: 0 30px 30px 30px;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
            font-size: 1.1em;
        }
        .section-content {
            padding: 20px;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Talent Hero Test Report</h1>
            <p>Comprehensive Testing Results</p>
        </div>
        
        <div class="summary">
            <div class="status-card" style="background-color: #dc3545">
                <h3>Overall Status</h3>
                <p>FAILED</p>
            </div>
            <div class="status-card" style="background-color: #dc3545">
                <h3>Frontend Tests</h3>
                <p>FAILED</p>
            </div>
            <div class="status-card" style="background-color: #dc3545">
                <h3>Backend Tests</h3>
                <p>FAILED</p>
            </div>
        </div>
        
        <div class="details">
            <div class="section">
                <div class="section-header">🎨 Frontend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> failed</p>
                    <p><strong>Return Code:</strong> 1</p>
                    
                    
                    
                    
            <h4>Frontend Output</h4>
            <div class="test-output">
> th-v3-11@0.0.0 test:run
> vitest run


[1m[46m RUN [49m[22m [36mv3.2.4 [39m[90mC:/Users/<USER>/IdeaProjects/th-v3-11[39m

 [32m✓[39m src/components/__tests__/Simple.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[32m 3[2mms[22m[39m
 [31m❯[39m src/components/__tests__/App.test.tsx [2m([22m[2m2 tests[22m[2m | [22m[31m2 failed[39m[2m)[22m[32m 18[2mms[22m[39m
[31m   [31m×[31m App Component[2m > [22mrenders without crashing[39m[32m 15[2mms[22m[39m
[31m     → You cannot render a <Router> inside another <Router>. You should never have more than one in your app.[39m
[31m   [31m×[31m App Component[2m > [22mhandles routing correctly[39m[32m 2[2mms[22m[39m
[31m     → You cannot render a <Router> inside another <Router>. You should never have more than one in your app.[39m
 [31m❯[39m src/components/__tests__/GroupManagement.test.tsx [2m([22m[2m11 tests[22m[2m | [22m[31m6 failed[39m[2m)[22m[32m 255[2mms[22m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mrenders group management interface[39m[32m 23[2mms[22m[39m
[31m     → Unable to find an element with the text: Group Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m GroupManagement Component[2m > [22mfetches groups on mount[32m 15[2mms[22m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mopens create group modal when add group button is clicked[39m[32m 27[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mcreates a new group[39m[32m 5[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mhandles search functionality[39m[32m 3[2mms[22m[39m
[31m     → Unable to find an element with the placeholder text of: /search groups/i

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m GroupManagement Component[2m > [22mopens group details when group name is clicked[32m 69[2mms[22m[39m
   [32m✓[39m GroupManagement Component[2m > [22mhandles pagination[32m 77[2mms[22m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mvalidates group name before creation[39m[32m 5[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[31m   [31m×[31m GroupManagement Component[2m > [22mcalls onBack when back button is clicked[39m[32m 3[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/back/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m GroupManagement Component[2m > [22mdisplays group statistics correctly[32m 22[2mms[22m[39m
   [32m✓[39m GroupManagement Component[2m > [22mhandles error states[32m 4[2mms[22m[39m
 [31m❯[39m src/components/__tests__/UserManagement.test.tsx [2m([22m[2m11 tests[22m[2m | [22m[31m6 failed[39m[2m)[22m[33m 654[2mms[22m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mrenders user management interface[39m[32m 24[2mms[22m[39m
[31m     → Unable to find an element with the text: User Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m UserManagement Component[2m > [22mfetches users on mount[32m 30[2mms[22m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mopens create user modal when add user button is clicked[39m[32m 27[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/add user/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mhandles search functionality[39m[32m 3[2mms[22m[39m
[31m     → Unable to find an element with the placeholder text of: /search users/i

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m UserManagement Component[2m > [22mhandles user selection for bulk operations[32m 83[2mms[22m[39m
   [32m✓[39m UserManagement Component[2m > [22mhandles select all functionality[32m 77[2mms[22m[39m
   [32m✓[39m UserManagement Component[2m > [22mperforms bulk operations[32m 185[2mms[22m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mhandles user status toggle[39m[32m 76[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/toggle user status/i`

Here are the accessible roles:

  button:

  Name "Back to Admin Panel":
  [36m<button[31m
    [33mclass[31m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[31m
  [36m/>[31m

  Name "Add User":
  [36m<button[31m
    [33mclass[31m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[31m
  [36m/>[31m

  Name "":
  [36m<button[31m
    [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
  [36m/>[31m

  Name "":
  [36m<button[31m
    [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
  [36m/>[31m

  Name "Previous":
  [36m<button[31m
    [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
    [33mdisabled[31m=[32m""[31m
  [36m/>[31m

  Name "Next":
  [36m<button[31m
    [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
    [33mdisabled[31m=[32m""[31m
  [36m/>[31m

  --------------------------------------------------
  paragraph:

  Name "":
  [36m<p[31m
    [33mclass[31m=[32m"text-muted-foreground"[31m
  [36m/>[31m

  --------------------------------------------------
  textbox:

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
    [33mplaceholder[31m=[32m"Search by name, email, or employee ID..."[31m
    [33mtype[31m=[32m"text"[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  --------------------------------------------------
  combobox:

  Name "":
  [36m<select[31m
    [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
  [36m/>[31m

  Name "":
  [36m<select[31m
    [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
  [36m/>[31m

  --------------------------------------------------
  option:

  Name "All Types":
  [36m<option[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  Name "Super Admin":
  [36m<option[31m
    [33mvalue[31m=[32m"superadmin"[31m
  [36m/>[31m

  Name "Admin":
  [36m<option[31m
    [33mvalue[31m=[32m"admin"[31m
  [36m/>[31m

  Name "User":
  [36m<option[31m
    [33mvalue[31m=[32m"user"[31m
  [36m/>[31m

  Name "Vendor":
  [36m<option[31m
    [33mvalue[31m=[32m"vendor"[31m
  [36m/>[31m

  Name "All Status":
  [36m<option[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  Name "Active":
  [36m<option[31m
    [33mvalue[31m=[32m"true"[31m
  [36m/>[31m

  Name "Inactive":
  [36m<option[31m
    [33mvalue[31m=[32m"false"[31m
  [36m/>[31m

  --------------------------------------------------
  table:

  Name "":
  [36m<table[31m
    [33mclass[31m=[32m"w-full"[31m
  [36m/>[31m

  --------------------------------------------------
  rowgroup:

  Name "":
  [36m<thead[31m
    [33mclass[31m=[32m"bg-muted/50"[31m
  [36m/>[31m

  Name "":
  [36m<tbody[31m
    [33mclass[31m=[32m"divide-y divide-border"[31m
  [36m/>[31m

  --------------------------------------------------
  row:

  Name "User Type Department Status Last Login Actions":
  [36m<tr />[31m

  Name "<NAME_EMAIL> ID: EMP001 admin IT Developer Active 1/1/2024":
  [36m<tr[31m
    [33mclass[31m=[32m"hover:bg-muted/50"[31m
  [36m/>[31m

  --------------------------------------------------
  columnheader:

  Name "":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "User":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Type":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Department":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Status":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Last Login":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Actions":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  --------------------------------------------------
  checkbox:

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"rounded border-input"[31m
    [33mtype[31m=[32m"checkbox"[31m
  [36m/>[31m

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"rounded border-input"[31m
    [33mtype[31m=[32m"checkbox"[31m
  [36m/>[31m

  --------------------------------------------------
  cell:

  Name "":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "<NAME_EMAIL> ID: EMP001":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "admin":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "IT Developer":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "Active":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "1/1/2024":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4 text-sm text-muted-foreground"[31m
  [36m/>[31m

  Name "":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  --------------------------------------------------

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6 space-y-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-between"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"flex items-center space-x-4"[31m
        [36m>[31m
          [36m<button[31m
            [33mclass[31m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[31m
          [36m>[31m
            [36m<svg[31m
              [33maria-hidden[31m=[32m"true"[31m
              [33mclass[31m=[32m"lucide lucide-arrow-left mr-2 h-4 w-4"[31m
              [33mfill[31m=[32m"none"[31m
              [33mheight[31m=[32m"24"[31m
              [33mstroke[31m=[32m"currentColor"[31m
              [33mstroke-linecap[31m=[32m"round"[31m
              [33mstroke-linejoin[31m=[32m"round"[31m
              [33mstroke-width[31m=[32m"2"[31m
              [33mviewBox[31m=[32m"0 0 24 24"[31m
              [33mwidth[31m=[32m"24"[31m
              [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
            [36m>[31m
              [36m<path[31m
                [33md[31m=[32m"m12 19-7-7 7-7"[31m
              [36m/>[31m
              [36m<path[31m
                [33md[31m=[32m"M19 12H5"[31m
              [36m/>[31m
            [36m</svg>[31m
            [0mBack to Admin Panel[0m
          [36m</button>[31m
          [36m<div>[31m
            [36m<p[31m
              [33mclass[31m=[32m"text-muted-foreground"[31m
            [36m>[31m
              [0mManage user accounts and permissions[0m
            [36m</p>[31m
          [36m</div>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"flex space-x-2"[31m
        [36m>[31m
          [36m<button[31m
            [33mclass[31m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[31m
          [36m>[31m
            [36m<svg[31m
              [33maria-hidden[31m=[32m"true"[31m
              [33mclass[31m=[32m"lucide lucide-plus h-4 w-4 mr-2"[31m
              [33mfill[31m=[32m"none"[31m
              [33mheight[31m=[32m"24"[31m
              [33mstroke[31m=[32m"currentColor"[31m
              [33mstroke-linecap[31m=[32m"round"[31m
              [33mstroke-linejoin[31m=[32m"round"[31m
              [33mstroke-width[31m=[32m"2"[31m
              [33mviewBox[31m=[32m"0 0 24 24"[31m
              [33mwidth[31m=[32m"24"[31m
              [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
            [36m>[31m
              [36m<path[31m
                [33md[31m=[32m"M5 12h14"[31m
              [36m/>[31m
              [36m<path[31m
                [33md[31m=[32m"M12 5v14"[31m
              [36m/>[31m
            [36m</svg>[31m
            [0mAdd User[0m
          [36m</button>[31m
        [36m</div>[31m
      [36m</div>[31m
      [36m<div[31m
        [33mclass[31m=[32m"bg-card rounded-lg border border-border p-4"[31m
      [36m>[31m
        [36m<form[31m
          [33mclass[31m=[32m"space-y-4"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"flex flex-col md:flex-row gap-4"[31m
          [36m>[31m
            [36m<div[31m
              [33mclass[31m=[32m"flex-1"[31m
            [36m>[31m
              [36m<div[31m
                [33mclass[31m=[32m"relative"[31m
              [36m>[31m
                [36m<svg[31m
                  [33maria-hidden[31m=[32m"true"[31m
                  [33mclass[31m=[32m"lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"[31m
                  [33mfill[31m=[32m"none"[31m
                  [33mheight[31m=[32m"24"[31m
                  [33mstroke[31m=[32m"currentColor"[31m
                  [33mstroke-linecap[31m=[32m"round"[31m
                  [33mstroke-linejoin[31m=[32m"round"[31m
                  [33mstroke-width[31m=[32m"2"[31m
                  [33mviewBox[31m=[32m"0 0 24 24"[31m
                  [33mwidth[31m=[32m"24"[31m
                  [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                [36m>[31m
                  [36m<path[31m
                    [33md[31m=[32m"m21 21-4.34-4.34"[31m
                  [36m/>[31m
                  [36m<circle[31m
                    [33mcx[31m=[32m"11"[31m
                    [33mcy[31m=[32m"11"[31m
                    [33mr[31m=[32m"8"[31m
                  [36m/>[31m
                [36m</svg>[31m
                [36m<input[31m
                  [33mclass[31m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
                  [33mplaceholder[31m=[32m"Search by name, email, or employee ID..."[31m
                  [33mtype[31m=[32m"text"[31m
                  [33mvalue[31m=[32m""[31m
                [36m/>[31m
              [36m</div>[31m
            [36m</div>[31m
            [36m<div[31m
              [33mclass[31m=[32m"flex gap-2"[31m
            [36m>[31m
              [36m<select[31m
                [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
              [36m>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m""[31m
                [36m>[31m
                  [0mAll Types[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"superadmin"[31m
                [36m>[31m
                  [0mSuper Admin[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"admin"[31m
                [36m>[31m
                  [0mAdmin[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"user"[31m
                [36m>[31m
                  [0mUser[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"vendor"[31m
                [36m>[31m
                  [0mVendor[0m
                [36m</option>[31m
              [36m</select>[31m
              [36m<select[31m
                [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
              [36m>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m""[31m
                [36m>[31m
                  [0mAll Status[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"true"[31m
                [36m>[31m
                  [0mActive[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"false"[31m
                [36m>[31m
                  [0mInactive[0m
                [36m</option>[31m
              [36m</select>[31m
            [36m</div>[31m
          [36m</div>[31m
        [36m</form>[31m
      [36m</div>[31m
      [36m<div[31m
        [33mclass[31m=[32m"bg-card rounded-lg border border-border overflow-hidden"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"overflow-x-auto"[31m
        [36m>[31m
          [36m<table[31m
            [33mclass[31m=[32m"w-full"[31m
          [36m>[31m
            [36m<thead[31m
              [33mclass[31m=[32m"bg-muted/50"[31m
            [36m>[31m
              [36m<tr>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [36m<input[31m
                    [33mclass[31m=[32m"rounded border-input"[31m
                    [33mtype[31m=[32m"checkbox"[31m
                  [36m/>[31m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mUser[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mType[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mDepartment[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mStatus[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mLast Login[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mActions[0m
                [36m</th>[31m
              [36m</tr>[31m
            [36m</thead>[31m
            [36m<tbody[31m
              [33mclass[31m=[32m"divide-y divide-border"[31m
            [36m>[31m
              [36m<tr[31m
                [33mclass[31m=[32m"hover:bg-muted/50"[31m
              [36m>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<input[31m
                    [33mclass[31m=[32m"rounded border-input"[31m
                    [33mtype[31m=[32m"checkbox"[31m
                  [36m/>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-sm font-medium text-card-foreground"[31m
                    [36m>[31m
                      [0mTest User[0m
                    [36m</div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
                    [36m>[31m
                      [<EMAIL>[0m
                    [36m</div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-xs text-muted-foreground"[31m
                    [36m>[31m
                      [0mID: [0m
                      [0mEMP001[0m
                    [36m</div>[31m
                  [36m</div>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<span[31m
                    [33mclass[31m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"[31m
                  [36m>[31m
                    [0madmin[0m
                  [36m</span>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"text-sm text-card-foreground"[31m
                  [36m>[31m
                    [0mIT[0m
                  [36m</div>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"text-xs text-muted-foreground"[31m
                  [36m>[31m
                    [0mDeveloper[0m
                  [36m</div>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<span[31m
                    [33mclass[31m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"[31m
                  [36m>[31m
                    [0mActive[0m
                  [36m</span>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4 text-sm text-muted-foreground"[31m
                [36m>[31m
                  [0m1/1/2024[0m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"flex items-center space-x-2"[31m
                  [36m>[31m
                    [36m<button[31m
                      [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
                    [36m>[31m
                      [36m<svg[31m
                        [33maria-hidden[31m=[32m"true"[31m
                        [33mclass[31m=[32m"lucide lucide-square-pen h-4 w-4 text-muted-foreground"[31m
                        [33mfill[31m=[32m"none"[31m
                        [33mheight[31m=[32m"24"[31m
                        [33mstroke[31m=[32m"currentColor"[31m
                        [33mstroke-linecap[31m=[32m"round"[31m
                        [33mstroke-linejoin[31m=[32m"round"[31m
                        [33mstroke-width[31m=[32m"2"[31m
                        [33mviewBox[31m=[32m"0 0 24 24"[31m
                        [33mwidth[31m=[32m"24"[31m
                        [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                      [36m>[31m
                        [36m<path[31m
                          [33md[31m=[32m"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"[31m
                        [36m/>[31m
                        [36m<path[31m
                          [33md[31m=[32m"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"[31m
                        [36m/>[31m
                      [36m</svg>[31m
                    [36m</button>[31m
                    [36m<button[31m
                      [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
                    [36m>[31m
                      [36m<svg[31m
                        [33maria-hidden[31m=[32m"true"[31m
                        [33mclass[31m=[32m"lucide lucide-toggle-right h-4 w-4 text-green-600"[31m
                        [33mfill[31m=[32m"none"[31m
                        [33mheight[31m=[32m"24"[31m
                        [33mstroke[31m=[32m"currentColor"[31m
                        [33mstroke-linecap[31m=[32m"round"[31m
                        [33mstroke-linejoin[31m=[32m"round"[31m
                        [33mstroke-width[31m=[32m"2"[31m
                        [33mviewBox[31m=[32m"0 0 24 24"[31m
                        [33mwidth[31m=[32m"24"[31m
                        [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                      [36m>[31m
                        [36m<circle[31m
                          [33mcx[31m=[32m"15"[31m
                          [33mcy[31m=[32m"12"[31m
                          [33mr[31m=[32m"3"[31m
                        [36m/>[31m
                        [36m<rect[31m
                          [33mheight[31m=[32m"14"[31m
                          [33mrx[31m=[32m"7"[31m
                          [33mwidth[31m=[32m"20"[31m
                          [33mx[31m=[32m"2"[31m
                          [33my[31m=[32m"5"[31m
                        [36m/>[31m
                      [36m</svg>[31m
                    [36m</button>[31m
                  [36m</div>[31m
                [36m</td>[31m
              [36m</tr>[31m
            [36m</tbody>[31m
          [36m</table>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"px-4 py-3 border-t border-border flex items-center justify-between"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
          [36m>[31m
            [0mShowing [0m
            [0m1[0m
            [0m of [0m
            [0m1[0m
            [0m users[0m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"flex items-center space-x-2"[31m
          [36m>[31m
            [36m<button[31m
              [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
              [33mdisabled[31m=[32m""[31m
            [36m>[31m
              [0mPrevious[0m
            [36m</button>[31m
            [36m<span[31m
              [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
            [36m>[31m
              [0mPage [0m
              [0m1[0m
              [0m of [0m
              [0m1[0m
            [36m</span>[31m
            [36m<button[31m
              [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
              [33mdisabled[31m=[32m""[31m
            [36m>[31m
              [0mNext[0m
            [36m</button>[31m
          [36m</div>[31m
        [36m</div>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mopens edit modal when edit button is clicked[39m[32m 64[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/edit user/i`

Here are the accessible roles:

  button:

  Name "Back to Admin Panel":
  [36m<button[31m
    [33mclass[31m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[31m
  [36m/>[31m

  Name "Add User":
  [36m<button[31m
    [33mclass[31m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[31m
  [36m/>[31m

  Name "":
  [36m<button[31m
    [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
  [36m/>[31m

  Name "":
  [36m<button[31m
    [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
  [36m/>[31m

  Name "Previous":
  [36m<button[31m
    [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
    [33mdisabled[31m=[32m""[31m
  [36m/>[31m

  Name "Next":
  [36m<button[31m
    [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
    [33mdisabled[31m=[32m""[31m
  [36m/>[31m

  --------------------------------------------------
  paragraph:

  Name "":
  [36m<p[31m
    [33mclass[31m=[32m"text-muted-foreground"[31m
  [36m/>[31m

  --------------------------------------------------
  textbox:

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
    [33mplaceholder[31m=[32m"Search by name, email, or employee ID..."[31m
    [33mtype[31m=[32m"text"[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  --------------------------------------------------
  combobox:

  Name "":
  [36m<select[31m
    [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
  [36m/>[31m

  Name "":
  [36m<select[31m
    [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
  [36m/>[31m

  --------------------------------------------------
  option:

  Name "All Types":
  [36m<option[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  Name "Super Admin":
  [36m<option[31m
    [33mvalue[31m=[32m"superadmin"[31m
  [36m/>[31m

  Name "Admin":
  [36m<option[31m
    [33mvalue[31m=[32m"admin"[31m
  [36m/>[31m

  Name "User":
  [36m<option[31m
    [33mvalue[31m=[32m"user"[31m
  [36m/>[31m

  Name "Vendor":
  [36m<option[31m
    [33mvalue[31m=[32m"vendor"[31m
  [36m/>[31m

  Name "All Status":
  [36m<option[31m
    [33mvalue[31m=[32m""[31m
  [36m/>[31m

  Name "Active":
  [36m<option[31m
    [33mvalue[31m=[32m"true"[31m
  [36m/>[31m

  Name "Inactive":
  [36m<option[31m
    [33mvalue[31m=[32m"false"[31m
  [36m/>[31m

  --------------------------------------------------
  table:

  Name "":
  [36m<table[31m
    [33mclass[31m=[32m"w-full"[31m
  [36m/>[31m

  --------------------------------------------------
  rowgroup:

  Name "":
  [36m<thead[31m
    [33mclass[31m=[32m"bg-muted/50"[31m
  [36m/>[31m

  Name "":
  [36m<tbody[31m
    [33mclass[31m=[32m"divide-y divide-border"[31m
  [36m/>[31m

  --------------------------------------------------
  row:

  Name "User Type Department Status Last Login Actions":
  [36m<tr />[31m

  Name "<NAME_EMAIL> ID: EMP001 admin IT Developer Active 1/1/2024":
  [36m<tr[31m
    [33mclass[31m=[32m"hover:bg-muted/50"[31m
  [36m/>[31m

  --------------------------------------------------
  columnheader:

  Name "":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "User":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Type":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Department":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Status":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Last Login":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  Name "Actions":
  [36m<th[31m
    [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
  [36m/>[31m

  --------------------------------------------------
  checkbox:

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"rounded border-input"[31m
    [33mtype[31m=[32m"checkbox"[31m
  [36m/>[31m

  Name "":
  [36m<input[31m
    [33mclass[31m=[32m"rounded border-input"[31m
    [33mtype[31m=[32m"checkbox"[31m
  [36m/>[31m

  --------------------------------------------------
  cell:

  Name "":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "<NAME_EMAIL> ID: EMP001":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "admin":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "IT Developer":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "Active":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  Name "1/1/2024":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4 text-sm text-muted-foreground"[31m
  [36m/>[31m

  Name "":
  [36m<td[31m
    [33mclass[31m=[32m"px-4 py-4"[31m
  [36m/>[31m

  --------------------------------------------------

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6 space-y-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-between"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"flex items-center space-x-4"[31m
        [36m>[31m
          [36m<button[31m
            [33mclass[31m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[31m
          [36m>[31m
            [36m<svg[31m
              [33maria-hidden[31m=[32m"true"[31m
              [33mclass[31m=[32m"lucide lucide-arrow-left mr-2 h-4 w-4"[31m
              [33mfill[31m=[32m"none"[31m
              [33mheight[31m=[32m"24"[31m
              [33mstroke[31m=[32m"currentColor"[31m
              [33mstroke-linecap[31m=[32m"round"[31m
              [33mstroke-linejoin[31m=[32m"round"[31m
              [33mstroke-width[31m=[32m"2"[31m
              [33mviewBox[31m=[32m"0 0 24 24"[31m
              [33mwidth[31m=[32m"24"[31m
              [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
            [36m>[31m
              [36m<path[31m
                [33md[31m=[32m"m12 19-7-7 7-7"[31m
              [36m/>[31m
              [36m<path[31m
                [33md[31m=[32m"M19 12H5"[31m
              [36m/>[31m
            [36m</svg>[31m
            [0mBack to Admin Panel[0m
          [36m</button>[31m
          [36m<div>[31m
            [36m<p[31m
              [33mclass[31m=[32m"text-muted-foreground"[31m
            [36m>[31m
              [0mManage user accounts and permissions[0m
            [36m</p>[31m
          [36m</div>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"flex space-x-2"[31m
        [36m>[31m
          [36m<button[31m
            [33mclass[31m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[31m
          [36m>[31m
            [36m<svg[31m
              [33maria-hidden[31m=[32m"true"[31m
              [33mclass[31m=[32m"lucide lucide-plus h-4 w-4 mr-2"[31m
              [33mfill[31m=[32m"none"[31m
              [33mheight[31m=[32m"24"[31m
              [33mstroke[31m=[32m"currentColor"[31m
              [33mstroke-linecap[31m=[32m"round"[31m
              [33mstroke-linejoin[31m=[32m"round"[31m
              [33mstroke-width[31m=[32m"2"[31m
              [33mviewBox[31m=[32m"0 0 24 24"[31m
              [33mwidth[31m=[32m"24"[31m
              [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
            [36m>[31m
              [36m<path[31m
                [33md[31m=[32m"M5 12h14"[31m
              [36m/>[31m
              [36m<path[31m
                [33md[31m=[32m"M12 5v14"[31m
              [36m/>[31m
            [36m</svg>[31m
            [0mAdd User[0m
          [36m</button>[31m
        [36m</div>[31m
      [36m</div>[31m
      [36m<div[31m
        [33mclass[31m=[32m"bg-card rounded-lg border border-border p-4"[31m
      [36m>[31m
        [36m<form[31m
          [33mclass[31m=[32m"space-y-4"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"flex flex-col md:flex-row gap-4"[31m
          [36m>[31m
            [36m<div[31m
              [33mclass[31m=[32m"flex-1"[31m
            [36m>[31m
              [36m<div[31m
                [33mclass[31m=[32m"relative"[31m
              [36m>[31m
                [36m<svg[31m
                  [33maria-hidden[31m=[32m"true"[31m
                  [33mclass[31m=[32m"lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"[31m
                  [33mfill[31m=[32m"none"[31m
                  [33mheight[31m=[32m"24"[31m
                  [33mstroke[31m=[32m"currentColor"[31m
                  [33mstroke-linecap[31m=[32m"round"[31m
                  [33mstroke-linejoin[31m=[32m"round"[31m
                  [33mstroke-width[31m=[32m"2"[31m
                  [33mviewBox[31m=[32m"0 0 24 24"[31m
                  [33mwidth[31m=[32m"24"[31m
                  [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                [36m>[31m
                  [36m<path[31m
                    [33md[31m=[32m"m21 21-4.34-4.34"[31m
                  [36m/>[31m
                  [36m<circle[31m
                    [33mcx[31m=[32m"11"[31m
                    [33mcy[31m=[32m"11"[31m
                    [33mr[31m=[32m"8"[31m
                  [36m/>[31m
                [36m</svg>[31m
                [36m<input[31m
                  [33mclass[31m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
                  [33mplaceholder[31m=[32m"Search by name, email, or employee ID..."[31m
                  [33mtype[31m=[32m"text"[31m
                  [33mvalue[31m=[32m""[31m
                [36m/>[31m
              [36m</div>[31m
            [36m</div>[31m
            [36m<div[31m
              [33mclass[31m=[32m"flex gap-2"[31m
            [36m>[31m
              [36m<select[31m
                [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
              [36m>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m""[31m
                [36m>[31m
                  [0mAll Types[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"superadmin"[31m
                [36m>[31m
                  [0mSuper Admin[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"admin"[31m
                [36m>[31m
                  [0mAdmin[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"user"[31m
                [36m>[31m
                  [0mUser[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"vendor"[31m
                [36m>[31m
                  [0mVendor[0m
                [36m</option>[31m
              [36m</select>[31m
              [36m<select[31m
                [33mclass[31m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[31m
              [36m>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m""[31m
                [36m>[31m
                  [0mAll Status[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"true"[31m
                [36m>[31m
                  [0mActive[0m
                [36m</option>[31m
                [36m<option[31m
                  [33mvalue[31m=[32m"false"[31m
                [36m>[31m
                  [0mInactive[0m
                [36m</option>[31m
              [36m</select>[31m
            [36m</div>[31m
          [36m</div>[31m
        [36m</form>[31m
      [36m</div>[31m
      [36m<div[31m
        [33mclass[31m=[32m"bg-card rounded-lg border border-border overflow-hidden"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"overflow-x-auto"[31m
        [36m>[31m
          [36m<table[31m
            [33mclass[31m=[32m"w-full"[31m
          [36m>[31m
            [36m<thead[31m
              [33mclass[31m=[32m"bg-muted/50"[31m
            [36m>[31m
              [36m<tr>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [36m<input[31m
                    [33mclass[31m=[32m"rounded border-input"[31m
                    [33mtype[31m=[32m"checkbox"[31m
                  [36m/>[31m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mUser[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mType[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mDepartment[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mStatus[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mLast Login[0m
                [36m</th>[31m
                [36m<th[31m
                  [33mclass[31m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[31m
                [36m>[31m
                  [0mActions[0m
                [36m</th>[31m
              [36m</tr>[31m
            [36m</thead>[31m
            [36m<tbody[31m
              [33mclass[31m=[32m"divide-y divide-border"[31m
            [36m>[31m
              [36m<tr[31m
                [33mclass[31m=[32m"hover:bg-muted/50"[31m
              [36m>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<input[31m
                    [33mclass[31m=[32m"rounded border-input"[31m
                    [33mtype[31m=[32m"checkbox"[31m
                  [36m/>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-sm font-medium text-card-foreground"[31m
                    [36m>[31m
                      [0mTest User[0m
                    [36m</div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
                    [36m>[31m
                      [<EMAIL>[0m
                    [36m</div>[31m
                    [36m<div[31m
                      [33mclass[31m=[32m"text-xs text-muted-foreground"[31m
                    [36m>[31m
                      [0mID: [0m
                      [0mEMP001[0m
                    [36m</div>[31m
                  [36m</div>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<span[31m
                    [33mclass[31m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"[31m
                  [36m>[31m
                    [0madmin[0m
                  [36m</span>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"text-sm text-card-foreground"[31m
                  [36m>[31m
                    [0mIT[0m
                  [36m</div>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"text-xs text-muted-foreground"[31m
                  [36m>[31m
                    [0mDeveloper[0m
                  [36m</div>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<span[31m
                    [33mclass[31m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"[31m
                  [36m>[31m
                    [0mActive[0m
                  [36m</span>[31m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4 text-sm text-muted-foreground"[31m
                [36m>[31m
                  [0m1/1/2024[0m
                [36m</td>[31m
                [36m<td[31m
                  [33mclass[31m=[32m"px-4 py-4"[31m
                [36m>[31m
                  [36m<div[31m
                    [33mclass[31m=[32m"flex items-center space-x-2"[31m
                  [36m>[31m
                    [36m<button[31m
                      [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
                    [36m>[31m
                      [36m<svg[31m
                        [33maria-hidden[31m=[32m"true"[31m
                        [33mclass[31m=[32m"lucide lucide-square-pen h-4 w-4 text-muted-foreground"[31m
                        [33mfill[31m=[32m"none"[31m
                        [33mheight[31m=[32m"24"[31m
                        [33mstroke[31m=[32m"currentColor"[31m
                        [33mstroke-linecap[31m=[32m"round"[31m
                        [33mstroke-linejoin[31m=[32m"round"[31m
                        [33mstroke-width[31m=[32m"2"[31m
                        [33mviewBox[31m=[32m"0 0 24 24"[31m
                        [33mwidth[31m=[32m"24"[31m
                        [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                      [36m>[31m
                        [36m<path[31m
                          [33md[31m=[32m"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"[31m
                        [36m/>[31m
                        [36m<path[31m
                          [33md[31m=[32m"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"[31m
                        [36m/>[31m
                      [36m</svg>[31m
                    [36m</button>[31m
                    [36m<button[31m
                      [33mclass[31m=[32m"p-1 rounded hover:bg-muted transition-colors"[31m
                    [36m>[31m
                      [36m<svg[31m
                        [33maria-hidden[31m=[32m"true"[31m
                        [33mclass[31m=[32m"lucide lucide-toggle-right h-4 w-4 text-green-600"[31m
                        [33mfill[31m=[32m"none"[31m
                        [33mheight[31m=[32m"24"[31m
                        [33mstroke[31m=[32m"currentColor"[31m
                        [33mstroke-linecap[31m=[32m"round"[31m
                        [33mstroke-linejoin[31m=[32m"round"[31m
                        [33mstroke-width[31m=[32m"2"[31m
                        [33mviewBox[31m=[32m"0 0 24 24"[31m
                        [33mwidth[31m=[32m"24"[31m
                        [33mxmlns[31m=[32m"http://www.w3.org/2000/svg"[31m
                      [36m>[31m
                        [36m<circle[31m
                          [33mcx[31m=[32m"15"[31m
                          [33mcy[31m=[32m"12"[31m
                          [33mr[31m=[32m"3"[31m
                        [36m/>[31m
                        [36m<rect[31m
                          [33mheight[31m=[32m"14"[31m
                          [33mrx[31m=[32m"7"[31m
                          [33mwidth[31m=[32m"20"[31m
                          [33mx[31m=[32m"2"[31m
                          [33my[31m=[32m"5"[31m
                        [36m/>[31m
                      [36m</svg>[31m
                    [36m</button>[31m
                  [36m</div>[31m
                [36m</td>[31m
              [36m</tr>[31m
            [36m</tbody>[31m
          [36m</table>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"px-4 py-3 border-t border-border flex items-center justify-between"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
          [36m>[31m
            [0mShowing [0m
            [0m1[0m
            [0m of [0m
            [0m1[0m
            [0m users[0m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"flex items-center space-x-2"[31m
          [36m>[31m
            [36m<button[31m
              [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
              [33mdisabled[31m=[32m""[31m
            [36m>[31m
              [0mPrevious[0m
            [36m</button>[31m
            [36m<span[31m
              [33mclass[31m=[32m"text-sm text-muted-foreground"[31m
            [36m>[31m
              [0mPage [0m
              [0m1[0m
              [0m of [0m
              [0m1[0m
            [36m</span>[31m
            [36m<button[31m
              [33mclass[31m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[31m
              [33mdisabled[31m=[32m""[31m
            [36m>[31m
              [0mNext[0m
            [36m</button>[31m
          [36m</div>[31m
        [36m</div>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
   [32m✓[39m UserManagement Component[2m > [22mhandles pagination[32m 80[2mms[22m[39m
[31m   [31m×[31m UserManagement Component[2m > [22mcalls onBack when back button is clicked[39m[32m 4[2mms[22m[39m
[31m     → Unable to find an accessible element with the role "button" and name `/back/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m

[2m Test Files [22m [1m[31m4 failed[39m[22m[2m | [22m[1m[32m1 passed[39m[22m[90m (5)[39m
[2m      Tests [22m [1m[31m14 failed[39m[22m[2m | [22m[1m[32m15 passed[39m[22m[90m (29)[39m
[2m   Start at [22m 00:31:42
[2m   Duration [22m 1.90s[2m (transform 181ms, setup 435ms, collect 728ms, tests 930ms, environment 2.04s, prepare 593ms)[22m

</div>
        
                    
            <h4>Frontend Errors</h4>
            <div class="error">[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mrenders group management interface
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/UserManagement.test.tsx[2m > [22m[2mUserManagement Component[2m > [22m[2mrenders user management interface
[22m[39mAn update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mopens create group modal when add group button is clicked
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mcreates a new group
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mhandles search functionality
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/UserManagement.test.tsx[2m > [22m[2mUserManagement Component[2m > [22m[2mopens create user modal when add user button is clicked
[22m[39mAn update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/UserManagement.test.tsx[2m > [22m[2mUserManagement Component[2m > [22m[2mhandles search functionality
[22m[39mAn update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mvalidates group name before creation
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/GroupManagement.test.tsx[2m > [22m[2mGroupManagement Component[2m > [22m[2mcalls onBack when back button is clicked
[22m[39mAn update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to GroupManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

[90mstderr[2m | src/components/__tests__/UserManagement.test.tsx[2m > [22m[2mUserManagement Component[2m > [22m[2mcalls onBack when back button is clicked
[22m[39mAn update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
An update to UserManagement inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act


[31m⎯⎯⎯⎯⎯⎯[39m[1m[41m Failed Suites 1 [49m[22m[31m⎯⎯⎯⎯⎯⎯⎯[39m

[41m[1m FAIL [22m[49m src/components/__tests__/Login.test.tsx[2m [ src/components/__tests__/Login.test.tsx ][22m
[31m[1mError[22m: Failed to resolve import "../Login" from "src/components/__tests__/Login.test.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/Login.test.tsx[39m:5:0
[33m  10 |  const __vi_import_2__ = await import("@testing-library/user-event");
  11 |  const __vi_import_3__ = await import("../../test/utils");
  12 |  const __vi_import_4__ = await import("../Login");
     |                                       ^
  13 |  
  14 |  import { describe, it, expect, vi, beforeEach } from "vitest";[39m
[90m [2m❯[22m TransformPluginContext._formatLog ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m31435:43[22m[39m
[90m [2m❯[22m TransformPluginContext.error ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m31432:14[22m[39m
[90m [2m❯[22m normalizeUrl ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m29978:18[22m[39m
[90m [2m❯[22m ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m30036:32[22m[39m
[90m [2m❯[22m TransformPluginContext.transform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m30004:4[22m[39m
[90m [2m❯[22m EnvironmentPluginContainer.transform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m31249:14[22m[39m
[90m [2m❯[22m loadAndTransform ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/vite/dist/node/chunks/dep-DZ2tZksn.js:[2m26419:26[22m[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/15]⎯[22m[39m


[31m⎯⎯⎯⎯⎯⎯[39m[1m[41m Failed Tests 14 [49m[22m[31m⎯⎯⎯⎯⎯⎯⎯[39m

[41m[1m FAIL [22m[49m src/components/__tests__/App.test.tsx[2m > [22mApp Component[2m > [22mrenders without crashing
[41m[1m FAIL [22m[49m src/components/__tests__/App.test.tsx[2m > [22mApp Component[2m > [22mhandles routing correctly
[31m[1mError[22m: You cannot render a <Router> inside another <Router>. You should never have more than one in your app.[39m
[90m [2m❯[22m invariant ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:[2m188:11[22m[39m
[90m [2m❯[22m Router ../../../ProX%20PC/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:[2m5746:3[22m[39m
[90m [2m❯[22m Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:[2m23863:20[22m[39m
[90m [2m❯[22m renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:[2m5529:22[22m[39m
[90m [2m❯[22m updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:[2m8897:19[22m[39m
[90m [2m❯[22m beginWork node_modules/react-dom/cjs/react-dom-client.development.js:[2m10522:18[22m[39m
[90m [2m❯[22m runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:[2m1522:13[22m[39m
[90m [2m❯[22m performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:[2m15140:22[22m[39m
[90m [2m❯[22m workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:[2m14956:41[22m[39m
[90m [2m❯[22m renderRootSync node_modules/react-dom/cjs/react-dom-client.development.js:[2m14936:11[22m[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mrenders group management interface
[31m[1mTestingLibraryElementError[22m: Unable to find an element with the text: Group Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m30:19[22m[39m
    [90m 28| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 29| [39m    
    [90m 30| [39m    [34mexpect[39m(screen[33m.[39m[34mgetByText[39m([32m'Group Management'[39m))[33m.[39m[34mtoBeInTheDocument[39m()
    [90m   | [39m                  [31m^[39m
    [90m 31| [39m    expect(screen.getByText('Manage user groups and permissions')).toB…
    [90m 32| [39m    expect(screen.getByRole('button', { name: /add group/i })).toBeInT…

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[3/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mopens create group modal when add group button is clicked
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m56:30[22m[39m
    [90m 54| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 55| [39m    
    [90m 56| [39m    const addButton = screen.getByRole('button', { name: /add group/i …
    [90m   | [39m                             [31m^[39m
    [90m 57| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(addButton)
    [90m 58| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[4/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mcreates a new group
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m91:30[22m[39m
    [90m 89| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 90| [39m    
    [90m 91| [39m    const addButton = screen.getByRole('button', { name: /add group/i …
    [90m   | [39m                             [31m^[39m
    [90m 92| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(addButton)
    [90m 93| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[5/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mhandles search functionality
[31m[1mTestingLibraryElementError[22m: Unable to find an element with the placeholder text of: /search groups/i

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m115:32[22m[39m
    [90m113| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m114| [39m    
    [90m115| [39m    [35mconst[39m searchInput [33m=[39m screen[33m.[39m[34mgetByPlaceholderText[39m([36m/search groups/i[39m)
    [90m   | [39m                               [31m^[39m
    [90m116| [39m    const searchButton = screen.getByRole('button', { name: /search/i …
    [90m117| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[6/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mvalidates group name before creation
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/add group/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m226:30[22m[39m
    [90m224| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m225| [39m    
    [90m226| [39m    const addButton = screen.getByRole('button', { name: /add group/i …
    [90m   | [39m                             [31m^[39m
    [90m227| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(addButton)
    [90m228| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[7/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/GroupManagement.test.tsx[2m > [22mGroupManagement Component[2m > [22mcalls onBack when back button is clicked
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/back/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"text-muted-foreground"[31m
      [36m>[31m
        [0mLoading groups...[0m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/GroupManagement.test.tsx:[2m239:31[22m[39m
    [90m237| [39m    [34mrender[39m([33m<[39m[33mGroupManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m238| [39m    
    [90m239| [39m    [35mconst[39m backButton [33m=[39m screen[33m.[39m[34mgetByRole[39m([32m'button'[39m[33m,[39m { name[33m:[39m [36m/back/i[39m })
    [90m   | [39m                              [31m^[39m
    [90m240| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(backButton)
    [90m241| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[8/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mrenders user management interface
[31m[1mTestingLibraryElementError[22m: Unable to find an element with the text: User Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m30:19[22m[39m
    [90m 28| [39m    [34mrender[39m([33m<[39m[33mUserManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 29| [39m    
    [90m 30| [39m    [34mexpect[39m(screen[33m.[39m[34mgetByText[39m([32m'User Management'[39m))[33m.[39m[34mtoBeInTheDocument[39m()
    [90m   | [39m                  [31m^[39m
    [90m 31| [39m    expect(screen.getByText('Manage system users and their permissions…
    [90m 32| [39m    expect(screen.getByRole('button', { name: /add user/i })).toBeInTh…

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[9/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mopens create user modal when add user button is clicked
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/add user/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m56:30[22m[39m
    [90m 54| [39m    [34mrender[39m([33m<[39m[33mUserManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 55| [39m    
    [90m 56| [39m    [35mconst[39m addButton [33m=[39m screen[33m.[39m[34mgetByRole[39m([32m'button'[39m[33m,[39m { name[33m:[39m [36m/add user/i[39m })
    [90m   | [39m                             [31m^[39m
    [90m 57| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(addButton)
    [90m 58| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[10/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mhandles search functionality
[31m[1mTestingLibraryElementError[22m: Unable to find an element with the placeholder text of: /search users/i

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m66:32[22m[39m
    [90m 64| [39m    [34mrender[39m([33m<[39m[33mUserManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m 65| [39m    
    [90m 66| [39m    [35mconst[39m searchInput [33m=[39m screen[33m.[39m[34mgetByPlaceholderText[39m([36m/search users/i[39m)
    [90m   | [39m                               [31m^[39m
    [90m 67| [39m    const searchButton = screen.getByRole('button', { name: /search/i …
    [90m 68| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[11/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mhandles user status toggle
[31m[1mTestingLibraryElementError[22m[39m: Unable to find an accessible element with the role "button" and name `/toggle user status/i`

Here are the accessible roles:

  button:

  Name "Back to Admin Panel":
  [36m<button[39m
    [33mclass[39m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[39m
  [36m/>[39m

  Name "Add User":
  [36m<button[39m
    [33mclass[39m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[39m
  [36m/>[39m

  Name "":
  [36m<button[39m
    [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
  [36m/>[39m

  Name "":
  [36m<button[39m
    [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
  [36m/>[39m

  Name "Previous":
  [36m<button[39m
    [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
    [33mdisabled[39m=[32m""[39m
  [36m/>[39m

  Name "Next":
  [36m<button[39m
    [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
    [33mdisabled[39m=[32m""[39m
  [36m/>[39m

  --------------------------------------------------
  paragraph:

  Name "":
  [36m<p[39m
    [33mclass[39m=[32m"text-muted-foreground"[39m
  [36m/>[39m

  --------------------------------------------------
  textbox:

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
    [33mplaceholder[39m=[32m"Search by name, email, or employee ID..."[39m
    [33mtype[39m=[32m"text"[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  --------------------------------------------------
  combobox:

  Name "":
  [36m<select[39m
    [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
  [36m/>[39m

  Name "":
  [36m<select[39m
    [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
  [36m/>[39m

  --------------------------------------------------
  option:

  Name "All Types":
  [36m<option[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  Name "Super Admin":
  [36m<option[39m
    [33mvalue[39m=[32m"superadmin"[39m
  [36m/>[39m

  Name "Admin":
  [36m<option[39m
    [33mvalue[39m=[32m"admin"[39m
  [36m/>[39m

  Name "User":
  [36m<option[39m
    [33mvalue[39m=[32m"user"[39m
  [36m/>[39m

  Name "Vendor":
  [36m<option[39m
    [33mvalue[39m=[32m"vendor"[39m
  [36m/>[39m

  Name "All Status":
  [36m<option[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  Name "Active":
  [36m<option[39m
    [33mvalue[39m=[32m"true"[39m
  [36m/>[39m

  Name "Inactive":
  [36m<option[39m
    [33mvalue[39m=[32m"false"[39m
  [36m/>[39m

  --------------------------------------------------
  table:

  Name "":
  [36m<table[39m
    [33mclass[39m=[32m"w-full"[39m
  [36m/>[39m

  --------------------------------------------------
  rowgroup:

  Name "":
  [36m<thead[39m
    [33mclass[39m=[32m"bg-muted/50"[39m
  [36m/>[39m

  Name "":
  [36m<tbody[39m
    [33mclass[39m=[32m"divide-y divide-border"[39m
  [36m/>[39m

  --------------------------------------------------
  row:

  Name "User Type Department Status Last Login Actions":
  [36m<tr />[39m

  Name "<NAME_EMAIL> ID: EMP001 admin IT Developer Active 1/1/2024":
  [36m<tr[39m
    [33mclass[39m=[32m"hover:bg-muted/50"[39m
  [36m/>[39m

  --------------------------------------------------
  columnheader:

  Name "":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "User":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Type":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Department":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Status":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Last Login":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Actions":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  --------------------------------------------------
  checkbox:

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"rounded border-input"[39m
    [33mtype[39m=[32m"checkbox"[39m
  [36m/>[39m

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"rounded border-input"[39m
    [33mtype[39m=[32m"checkbox"[39m
  [36m/>[39m

  --------------------------------------------------
  cell:

  Name "":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "<NAME_EMAIL> ID: EMP001":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "admin":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "IT Developer":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "Active":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "1/1/2024":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4 text-sm text-muted-foreground"[39m
  [36m/>[39m

  Name "":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  --------------------------------------------------

Ignored nodes: comments, script, style
[36m<body>[39m
  [36m<div>[39m
    [36m<div[39m
      [33mclass[39m=[32m"p-6 space-y-6"[39m
    [36m>[39m
      [36m<div[39m
        [33mclass[39m=[32m"flex items-center justify-between"[39m
      [36m>[39m
        [36m<div[39m
          [33mclass[39m=[32m"flex items-center space-x-4"[39m
        [36m>[39m
          [36m<button[39m
            [33mclass[39m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[39m
          [36m>[39m
            [36m<svg[39m
              [33maria-hidden[39m=[32m"true"[39m
              [33mclass[39m=[32m"lucide lucide-arrow-left mr-2 h-4 w-4"[39m
              [33mfill[39m=[32m"none"[39m
              [33mheight[39m=[32m"24"[39m
              [33mstroke[39m=[32m"currentColor"[39m
              [33mstroke-linecap[39m=[32m"round"[39m
              [33mstroke-linejoin[39m=[32m"round"[39m
              [33mstroke-width[39m=[32m"2"[39m
              [33mviewBox[39m=[32m"0 0 24 24"[39m
              [33mwidth[39m=[32m"24"[39m
              [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
            [36m>[39m
              [36m<path[39m
                [33md[39m=[32m"m12 19-7-7 7-7"[39m
              [36m/>[39m
              [36m<path[39m
                [33md[39m=[32m"M19 12H5"[39m
              [36m/>[39m
            [36m</svg>[39m
            [0mBack to Admin Panel[0m
          [36m</button>[39m
          [36m<div>[39m
            [36m<p[39m
              [33mclass[39m=[32m"text-muted-foreground"[39m
            [36m>[39m
              [0mManage user accounts and permissions[0m
            [36m</p>[39m
          [36m</div>[39m
        [36m</div>[39m
        [36m<div[39m
          [33mclass[39m=[32m"flex space-x-2"[39m
        [36m>[39m
          [36m<button[39m
            [33mclass[39m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[39m
          [36m>[39m
            [36m<svg[39m
              [33maria-hidden[39m=[32m"true"[39m
              [33mclass[39m=[32m"lucide lucide-plus h-4 w-4 mr-2"[39m
              [33mfill[39m=[32m"none"[39m
              [33mheight[39m=[32m"24"[39m
              [33mstroke[39m=[32m"currentColor"[39m
              [33mstroke-linecap[39m=[32m"round"[39m
              [33mstroke-linejoin[39m=[32m"round"[39m
              [33mstroke-width[39m=[32m"2"[39m
              [33mviewBox[39m=[32m"0 0 24 24"[39m
              [33mwidth[39m=[32m"24"[39m
              [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
            [36m>[39m
              [36m<path[39m
                [33md[39m=[32m"M5 12h14"[39m
              [36m/>[39m
              [36m<path[39m
                [33md[39m=[32m"M12 5v14"[39m
              [36m/>[39m
            [36m</svg>[39m
            [0mAdd User[0m
          [36m</button>[39m
        [36m</div>[39m
      [36m</div>[39m
      [36m<div[39m
        [33mclass[39m=[32m"bg-card rounded-lg border border-border p-4"[39m
      [36m>[39m
        [36m<form[39m
          [33mclass[39m=[32m"space-y-4"[39m
        [36m>[39m
          [36m<div[39m
            [33mclass[39m=[32m"flex flex-col md:flex-row gap-4"[39m
          [36m>[39m
            [36m<div[39m
              [33mclass[39m=[32m"flex-1"[39m
            [36m>[39m
              [36m<div[39m
                [33mclass[39m=[32m"relative"[39m
              [36m>[39m
                [36m<svg[39m
                  [33maria-hidden[39m=[32m"true"[39m
                  [33mclass[39m=[32m"lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"[39m
                  [33mfill[39m=[32m"none"[39m
                  [33mheight[39m=[32m"24"[39m
                  [33mstroke[39m=[32m"currentColor"[39m
                  [33mstroke-linecap[39m=[32m"round"[39m
                  [33mstroke-linejoin[39m=[32m"round"[39m
                  [33mstroke-width[39m=[32m"2"[39m
                  [33mviewBox[39m=[32m"0 0 24 24"[39m
                  [33mwidth[39m=[32m"24"[39m
                  [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                [36m>[39m
                  [36m<path[39m
                    [33md[39m=[32m"m21 21-4.34-4.34"[39m
                  [36m/>[39m
                  [36m<circle[39m
                    [33mcx[39m=[32m"11"[39m
                    [33mcy[39m=[32m"11"[39m
                    [33mr[39m=[32m"8"[39m
                  [36m/>[39m
                [36m</svg>[39m
                [36m<input[39m
                  [33mclass[39m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
                  [33mplaceholder[39m=[32m"Search by name, email, or employee ID..."[39m
                  [33mtype[39m=[32m"text"[39m
                  [33mvalue[39m=[32m""[39m
                [36m/>[39m
              [36m</div>[39m
            [36m</div>[39m
            [36m<div[39m
              [33mclass[39m=[32m"flex gap-2"[39m
            [36m>[39m
              [36m<select[39m
                [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
              [36m>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m""[39m
                [36m>[39m
                  [0mAll Types[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"superadmin"[39m
                [36m>[39m
                  [0mSuper Admin[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"admin"[39m
                [36m>[39m
                  [0mAdmin[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"user"[39m
                [36m>[39m
                  [0mUser[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"vendor"[39m
                [36m>[39m
                  [0mVendor[0m
                [36m</option>[39m
              [36m</select>[39m
              [36m<select[39m
                [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
              [36m>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m""[39m
                [36m>[39m
                  [0mAll Status[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"true"[39m
                [36m>[39m
                  [0mActive[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"false"[39m
                [36m>[39m
                  [0mInactive[0m
                [36m</option>[39m
              [36m</select>[39m
            [36m</div>[39m
          [36m</div>[39m
        [36m</form>[39m
      [36m</div>[39m
      [36m<div[39m
        [33mclass[39m=[32m"bg-card rounded-lg border border-border overflow-hidden"[39m
      [36m>[39m
        [36m<div[39m
          [33mclass[39m=[32m"overflow-x-auto"[39m
        [36m>[39m
          [36m<table[39m
            [33mclass[39m=[32m"w-full"[39m
          [36m>[39m
            [36m<thead[39m
              [33mclass[39m=[32m"bg-muted/50"[39m
            [36m>[39m
              [36m<tr>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [36m<input[39m
                    [33mclass[39m=[32m"rounded border-input"[39m
                    [33mtype[39m=[32m"checkbox"[39m
                  [36m/>[39m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mUser[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mType[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mDepartment[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mStatus[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mLast Login[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mActions[0m
                [36m</th>[39m
              [36m</tr>[39m
            [36m</thead>[39m
            [36m<tbody[39m
              [33mclass[39m=[32m"divide-y divide-border"[39m
            [36m>[39m
              [36m<tr[39m
                [33mclass[39m=[32m"hover:bg-muted/50"[39m
              [36m>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<input[39m
                    [33mclass[39m=[32m"rounded border-input"[39m
                    [33mtype[39m=[32m"checkbox"[39m
                  [36m/>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-sm font-medium text-card-foreground"[39m
                    [36m>[39m
                      [0mTest User[0m
                    [36m</div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
                    [36m>[39m
                      [<EMAIL>[0m
                    [36m</div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-xs text-muted-foreground"[39m
                    [36m>[39m
                      [0mID: [0m
                      [0mEMP001[0m
                    [36m</div>[39m
                  [36m</div>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<span[39m
                    [33mclass[39m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"[39m
                  [36m>[39m
                    [0madmin[0m
                  [36m</span>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"text-sm text-card-foreground"[39m
                  [36m>[39m
                    [0mIT[0m
                  [36m</div>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"text-xs text-muted-foreground"[39m
                  [36m>[39m
                    [0mDeveloper[0m
                  [36m</div>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<span[39m
                    [33mclass[39m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"[39m
                  [36m>[39m
                    [0mActive[0m
                  [36m</span>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4 text-sm text-muted-foreground"[39m
                [36m>[39m
                  [0m1/1/2024[0m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"flex items-center space-x-2"[39m
                  [36m>[39m
                    [36m<button[39m
                      [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
                    [36m>[39m
                      [36m<svg[39m
                        [33maria-hidden[39m=[32m"true"[39m
                        [33mclass[39m=[32m"lucide lucide-square-pen h-4 w-4 text-muted-foreground"[39m
                        [33mfill[39m=[32m"none"[39m
                        [33mheight[39m=[32m"24"[39m
                        [33mstroke[39m=[32m"currentColor"[39m
                        [33mstroke-linecap[39m=[32m"round"[39m
                        [33mstroke-linejoin[39m=[32m"round"[39m
                        [33mstroke-width[39m=[32m"2"[39m
                        [33mviewBox[39m=[32m"0 0 24 24"[39m
                        [33mwidth[39m=[32m"24"[39m
                        [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                      [36m>[39m
                        [36m<path[39m
                          [33md[39m=[32m"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"[39m
                        [36m/>[39m
                        [36m<path[39m
                          [33md[39m=[32m"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"[39m
                        [36m/>[39m
                      [36m</svg>[39m
                    [36m</button>[39m
                    [36m<button[39m
                      [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
                    [36m>[39m
                      [36m<svg[39m
                        [33maria-hidden[39m=[32m"true"[39m
                        [33mclass[39m=[32m"lucide lucide-toggle-right h-4 w-4 text-green-600"[39m
                        [33mfill[39m=[32m"none"[39m
                        [33mheight[39m=[32m"24"[39m
                        [33mstroke[39m=[32m"currentColor"[39m
                        [33mstroke-linecap[39m=[32m"round"[39m
                        [33mstroke-linejoin[39m=[32m"round"[39m
                        [33mstroke-width[39m=[32m"2"[39m
                        [33mviewBox[39m=[32m"0 0 24 24"[39m
                        [33mwidth[39m=[32m"24"[39m
                        [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                      [36m>[39m
                        [36m<circle[39m
                          [33mcx[39m=[32m"15"[39m
                          [33mcy[39m=[32m"12"[39m
                          [33mr[39m=[32m"3"[39m
                        [36m/>[39m
                        [36m<rect[39m
                          [33mheight[39m=[32m"14"[39m
                          [33mrx[39m=[32m"7"[39m
                          [33mwidth[39m=[32m"20"[39m
                          [33mx[39m=[32m"2"[39m
                          [33my[39m=[32m"5"[39m
                        [36m/>[39m
                      [36m</svg>[39m
                    [36m</button>[39m
                  [36m</div>[39m
                [36m</td>[39m
              [36m</tr>[39m
            [36m</tbody>[39m
          [36m</table>[39m
        [36m</div>[39m
        [36m<div[39m
          [33mclass[39m=[32m"px-4 py-3 border-t border-border flex items-center justify-between"[39m
        [36m>[39m
          [36m<div[39m
            [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
          [36m>[39m
            [0mShowing [0m
            [0m1[0m
            [0m of [0m
            [0m1[0m
            [0m users[0m
          [36m</div>[39m
          [36m<div[39m
            [33mclass[39m=[32m"flex items-center space-x-2"[39m
          [36m>[39m
            [36m<button[39m
              [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
              [33mdisabled[39m=[32m""[39m
            [36m>[39m
              [0mPrevious[0m
            [36m</button>[39m
            [36m<span[39m
              [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
            [36m>[39m
              [0mPage [0m
              [0m1[0m
              [0m of [0m
              [0m1[0m
            [36m</span>[39m
            [36m<button[39m
              [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
              [33mdisabled[39m=[32m""[39m
            [36m>[39m
              [0mNext[0m
            [36m</button>[39m
          [36m</div>[39m
        [36m</div>[39m
      [36m</div>[39m
    [36m</div>[39m
  [36m</div>[39m
[36m</body>[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m199:33[22m[39m
    [90m197| [39m    })
    [90m198| [39m
    [90m199| [39m    const toggleButton = screen.getByRole('button', { name: /toggle us…
    [90m   | [39m                                [31m^[39m
    [90m200| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(toggleButton)
    [90m201| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[12/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mopens edit modal when edit button is clicked
[31m[1mTestingLibraryElementError[22m[39m: Unable to find an accessible element with the role "button" and name `/edit user/i`

Here are the accessible roles:

  button:

  Name "Back to Admin Panel":
  [36m<button[39m
    [33mclass[39m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[39m
  [36m/>[39m

  Name "Add User":
  [36m<button[39m
    [33mclass[39m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[39m
  [36m/>[39m

  Name "":
  [36m<button[39m
    [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
  [36m/>[39m

  Name "":
  [36m<button[39m
    [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
  [36m/>[39m

  Name "Previous":
  [36m<button[39m
    [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
    [33mdisabled[39m=[32m""[39m
  [36m/>[39m

  Name "Next":
  [36m<button[39m
    [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
    [33mdisabled[39m=[32m""[39m
  [36m/>[39m

  --------------------------------------------------
  paragraph:

  Name "":
  [36m<p[39m
    [33mclass[39m=[32m"text-muted-foreground"[39m
  [36m/>[39m

  --------------------------------------------------
  textbox:

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
    [33mplaceholder[39m=[32m"Search by name, email, or employee ID..."[39m
    [33mtype[39m=[32m"text"[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  --------------------------------------------------
  combobox:

  Name "":
  [36m<select[39m
    [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
  [36m/>[39m

  Name "":
  [36m<select[39m
    [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
  [36m/>[39m

  --------------------------------------------------
  option:

  Name "All Types":
  [36m<option[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  Name "Super Admin":
  [36m<option[39m
    [33mvalue[39m=[32m"superadmin"[39m
  [36m/>[39m

  Name "Admin":
  [36m<option[39m
    [33mvalue[39m=[32m"admin"[39m
  [36m/>[39m

  Name "User":
  [36m<option[39m
    [33mvalue[39m=[32m"user"[39m
  [36m/>[39m

  Name "Vendor":
  [36m<option[39m
    [33mvalue[39m=[32m"vendor"[39m
  [36m/>[39m

  Name "All Status":
  [36m<option[39m
    [33mvalue[39m=[32m""[39m
  [36m/>[39m

  Name "Active":
  [36m<option[39m
    [33mvalue[39m=[32m"true"[39m
  [36m/>[39m

  Name "Inactive":
  [36m<option[39m
    [33mvalue[39m=[32m"false"[39m
  [36m/>[39m

  --------------------------------------------------
  table:

  Name "":
  [36m<table[39m
    [33mclass[39m=[32m"w-full"[39m
  [36m/>[39m

  --------------------------------------------------
  rowgroup:

  Name "":
  [36m<thead[39m
    [33mclass[39m=[32m"bg-muted/50"[39m
  [36m/>[39m

  Name "":
  [36m<tbody[39m
    [33mclass[39m=[32m"divide-y divide-border"[39m
  [36m/>[39m

  --------------------------------------------------
  row:

  Name "User Type Department Status Last Login Actions":
  [36m<tr />[39m

  Name "<NAME_EMAIL> ID: EMP001 admin IT Developer Active 1/1/2024":
  [36m<tr[39m
    [33mclass[39m=[32m"hover:bg-muted/50"[39m
  [36m/>[39m

  --------------------------------------------------
  columnheader:

  Name "":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "User":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Type":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Department":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Status":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Last Login":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  Name "Actions":
  [36m<th[39m
    [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
  [36m/>[39m

  --------------------------------------------------
  checkbox:

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"rounded border-input"[39m
    [33mtype[39m=[32m"checkbox"[39m
  [36m/>[39m

  Name "":
  [36m<input[39m
    [33mclass[39m=[32m"rounded border-input"[39m
    [33mtype[39m=[32m"checkbox"[39m
  [36m/>[39m

  --------------------------------------------------
  cell:

  Name "":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "<NAME_EMAIL> ID: EMP001":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "admin":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "IT Developer":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "Active":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  Name "1/1/2024":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4 text-sm text-muted-foreground"[39m
  [36m/>[39m

  Name "":
  [36m<td[39m
    [33mclass[39m=[32m"px-4 py-4"[39m
  [36m/>[39m

  --------------------------------------------------

Ignored nodes: comments, script, style
[36m<body>[39m
  [36m<div>[39m
    [36m<div[39m
      [33mclass[39m=[32m"p-6 space-y-6"[39m
    [36m>[39m
      [36m<div[39m
        [33mclass[39m=[32m"flex items-center justify-between"[39m
      [36m>[39m
        [36m<div[39m
          [33mclass[39m=[32m"flex items-center space-x-4"[39m
        [36m>[39m
          [36m<button[39m
            [33mclass[39m=[32m"inline-flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"[39m
          [36m>[39m
            [36m<svg[39m
              [33maria-hidden[39m=[32m"true"[39m
              [33mclass[39m=[32m"lucide lucide-arrow-left mr-2 h-4 w-4"[39m
              [33mfill[39m=[32m"none"[39m
              [33mheight[39m=[32m"24"[39m
              [33mstroke[39m=[32m"currentColor"[39m
              [33mstroke-linecap[39m=[32m"round"[39m
              [33mstroke-linejoin[39m=[32m"round"[39m
              [33mstroke-width[39m=[32m"2"[39m
              [33mviewBox[39m=[32m"0 0 24 24"[39m
              [33mwidth[39m=[32m"24"[39m
              [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
            [36m>[39m
              [36m<path[39m
                [33md[39m=[32m"m12 19-7-7 7-7"[39m
              [36m/>[39m
              [36m<path[39m
                [33md[39m=[32m"M19 12H5"[39m
              [36m/>[39m
            [36m</svg>[39m
            [0mBack to Admin Panel[0m
          [36m</button>[39m
          [36m<div>[39m
            [36m<p[39m
              [33mclass[39m=[32m"text-muted-foreground"[39m
            [36m>[39m
              [0mManage user accounts and permissions[0m
            [36m</p>[39m
          [36m</div>[39m
        [36m</div>[39m
        [36m<div[39m
          [33mclass[39m=[32m"flex space-x-2"[39m
        [36m>[39m
          [36m<button[39m
            [33mclass[39m=[32m"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"[39m
          [36m>[39m
            [36m<svg[39m
              [33maria-hidden[39m=[32m"true"[39m
              [33mclass[39m=[32m"lucide lucide-plus h-4 w-4 mr-2"[39m
              [33mfill[39m=[32m"none"[39m
              [33mheight[39m=[32m"24"[39m
              [33mstroke[39m=[32m"currentColor"[39m
              [33mstroke-linecap[39m=[32m"round"[39m
              [33mstroke-linejoin[39m=[32m"round"[39m
              [33mstroke-width[39m=[32m"2"[39m
              [33mviewBox[39m=[32m"0 0 24 24"[39m
              [33mwidth[39m=[32m"24"[39m
              [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
            [36m>[39m
              [36m<path[39m
                [33md[39m=[32m"M5 12h14"[39m
              [36m/>[39m
              [36m<path[39m
                [33md[39m=[32m"M12 5v14"[39m
              [36m/>[39m
            [36m</svg>[39m
            [0mAdd User[0m
          [36m</button>[39m
        [36m</div>[39m
      [36m</div>[39m
      [36m<div[39m
        [33mclass[39m=[32m"bg-card rounded-lg border border-border p-4"[39m
      [36m>[39m
        [36m<form[39m
          [33mclass[39m=[32m"space-y-4"[39m
        [36m>[39m
          [36m<div[39m
            [33mclass[39m=[32m"flex flex-col md:flex-row gap-4"[39m
          [36m>[39m
            [36m<div[39m
              [33mclass[39m=[32m"flex-1"[39m
            [36m>[39m
              [36m<div[39m
                [33mclass[39m=[32m"relative"[39m
              [36m>[39m
                [36m<svg[39m
                  [33maria-hidden[39m=[32m"true"[39m
                  [33mclass[39m=[32m"lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"[39m
                  [33mfill[39m=[32m"none"[39m
                  [33mheight[39m=[32m"24"[39m
                  [33mstroke[39m=[32m"currentColor"[39m
                  [33mstroke-linecap[39m=[32m"round"[39m
                  [33mstroke-linejoin[39m=[32m"round"[39m
                  [33mstroke-width[39m=[32m"2"[39m
                  [33mviewBox[39m=[32m"0 0 24 24"[39m
                  [33mwidth[39m=[32m"24"[39m
                  [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                [36m>[39m
                  [36m<path[39m
                    [33md[39m=[32m"m21 21-4.34-4.34"[39m
                  [36m/>[39m
                  [36m<circle[39m
                    [33mcx[39m=[32m"11"[39m
                    [33mcy[39m=[32m"11"[39m
                    [33mr[39m=[32m"8"[39m
                  [36m/>[39m
                [36m</svg>[39m
                [36m<input[39m
                  [33mclass[39m=[32m"flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
                  [33mplaceholder[39m=[32m"Search by name, email, or employee ID..."[39m
                  [33mtype[39m=[32m"text"[39m
                  [33mvalue[39m=[32m""[39m
                [36m/>[39m
              [36m</div>[39m
            [36m</div>[39m
            [36m<div[39m
              [33mclass[39m=[32m"flex gap-2"[39m
            [36m>[39m
              [36m<select[39m
                [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
              [36m>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m""[39m
                [36m>[39m
                  [0mAll Types[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"superadmin"[39m
                [36m>[39m
                  [0mSuper Admin[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"admin"[39m
                [36m>[39m
                  [0mAdmin[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"user"[39m
                [36m>[39m
                  [0mUser[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"vendor"[39m
                [36m>[39m
                  [0mVendor[0m
                [36m</option>[39m
              [36m</select>[39m
              [36m<select[39m
                [33mclass[39m=[32m"flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"[39m
              [36m>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m""[39m
                [36m>[39m
                  [0mAll Status[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"true"[39m
                [36m>[39m
                  [0mActive[0m
                [36m</option>[39m
                [36m<option[39m
                  [33mvalue[39m=[32m"false"[39m
                [36m>[39m
                  [0mInactive[0m
                [36m</option>[39m
              [36m</select>[39m
            [36m</div>[39m
          [36m</div>[39m
        [36m</form>[39m
      [36m</div>[39m
      [36m<div[39m
        [33mclass[39m=[32m"bg-card rounded-lg border border-border overflow-hidden"[39m
      [36m>[39m
        [36m<div[39m
          [33mclass[39m=[32m"overflow-x-auto"[39m
        [36m>[39m
          [36m<table[39m
            [33mclass[39m=[32m"w-full"[39m
          [36m>[39m
            [36m<thead[39m
              [33mclass[39m=[32m"bg-muted/50"[39m
            [36m>[39m
              [36m<tr>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [36m<input[39m
                    [33mclass[39m=[32m"rounded border-input"[39m
                    [33mtype[39m=[32m"checkbox"[39m
                  [36m/>[39m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mUser[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mType[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mDepartment[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mStatus[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mLast Login[0m
                [36m</th>[39m
                [36m<th[39m
                  [33mclass[39m=[32m"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"[39m
                [36m>[39m
                  [0mActions[0m
                [36m</th>[39m
              [36m</tr>[39m
            [36m</thead>[39m
            [36m<tbody[39m
              [33mclass[39m=[32m"divide-y divide-border"[39m
            [36m>[39m
              [36m<tr[39m
                [33mclass[39m=[32m"hover:bg-muted/50"[39m
              [36m>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<input[39m
                    [33mclass[39m=[32m"rounded border-input"[39m
                    [33mtype[39m=[32m"checkbox"[39m
                  [36m/>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-sm font-medium text-card-foreground"[39m
                    [36m>[39m
                      [0mTest User[0m
                    [36m</div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
                    [36m>[39m
                      [<EMAIL>[0m
                    [36m</div>[39m
                    [36m<div[39m
                      [33mclass[39m=[32m"text-xs text-muted-foreground"[39m
                    [36m>[39m
                      [0mID: [0m
                      [0mEMP001[0m
                    [36m</div>[39m
                  [36m</div>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<span[39m
                    [33mclass[39m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"[39m
                  [36m>[39m
                    [0madmin[0m
                  [36m</span>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"text-sm text-card-foreground"[39m
                  [36m>[39m
                    [0mIT[0m
                  [36m</div>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"text-xs text-muted-foreground"[39m
                  [36m>[39m
                    [0mDeveloper[0m
                  [36m</div>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<span[39m
                    [33mclass[39m=[32m"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"[39m
                  [36m>[39m
                    [0mActive[0m
                  [36m</span>[39m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4 text-sm text-muted-foreground"[39m
                [36m>[39m
                  [0m1/1/2024[0m
                [36m</td>[39m
                [36m<td[39m
                  [33mclass[39m=[32m"px-4 py-4"[39m
                [36m>[39m
                  [36m<div[39m
                    [33mclass[39m=[32m"flex items-center space-x-2"[39m
                  [36m>[39m
                    [36m<button[39m
                      [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
                    [36m>[39m
                      [36m<svg[39m
                        [33maria-hidden[39m=[32m"true"[39m
                        [33mclass[39m=[32m"lucide lucide-square-pen h-4 w-4 text-muted-foreground"[39m
                        [33mfill[39m=[32m"none"[39m
                        [33mheight[39m=[32m"24"[39m
                        [33mstroke[39m=[32m"currentColor"[39m
                        [33mstroke-linecap[39m=[32m"round"[39m
                        [33mstroke-linejoin[39m=[32m"round"[39m
                        [33mstroke-width[39m=[32m"2"[39m
                        [33mviewBox[39m=[32m"0 0 24 24"[39m
                        [33mwidth[39m=[32m"24"[39m
                        [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                      [36m>[39m
                        [36m<path[39m
                          [33md[39m=[32m"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"[39m
                        [36m/>[39m
                        [36m<path[39m
                          [33md[39m=[32m"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z"[39m
                        [36m/>[39m
                      [36m</svg>[39m
                    [36m</button>[39m
                    [36m<button[39m
                      [33mclass[39m=[32m"p-1 rounded hover:bg-muted transition-colors"[39m
                    [36m>[39m
                      [36m<svg[39m
                        [33maria-hidden[39m=[32m"true"[39m
                        [33mclass[39m=[32m"lucide lucide-toggle-right h-4 w-4 text-green-600"[39m
                        [33mfill[39m=[32m"none"[39m
                        [33mheight[39m=[32m"24"[39m
                        [33mstroke[39m=[32m"currentColor"[39m
                        [33mstroke-linecap[39m=[32m"round"[39m
                        [33mstroke-linejoin[39m=[32m"round"[39m
                        [33mstroke-width[39m=[32m"2"[39m
                        [33mviewBox[39m=[32m"0 0 24 24"[39m
                        [33mwidth[39m=[32m"24"[39m
                        [33mxmlns[39m=[32m"http://www.w3.org/2000/svg"[39m
                      [36m>[39m
                        [36m<circle[39m
                          [33mcx[39m=[32m"15"[39m
                          [33mcy[39m=[32m"12"[39m
                          [33mr[39m=[32m"3"[39m
                        [36m/>[39m
                        [36m<rect[39m
                          [33mheight[39m=[32m"14"[39m
                          [33mrx[39m=[32m"7"[39m
                          [33mwidth[39m=[32m"20"[39m
                          [33mx[39m=[32m"2"[39m
                          [33my[39m=[32m"5"[39m
                        [36m/>[39m
                      [36m</svg>[39m
                    [36m</button>[39m
                  [36m</div>[39m
                [36m</td>[39m
              [36m</tr>[39m
            [36m</tbody>[39m
          [36m</table>[39m
        [36m</div>[39m
        [36m<div[39m
          [33mclass[39m=[32m"px-4 py-3 border-t border-border flex items-center justify-between"[39m
        [36m>[39m
          [36m<div[39m
            [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
          [36m>[39m
            [0mShowing [0m
            [0m1[0m
            [0m of [0m
            [0m1[0m
            [0m users[0m
          [36m</div>[39m
          [36m<div[39m
            [33mclass[39m=[32m"flex items-center space-x-2"[39m
          [36m>[39m
            [36m<button[39m
              [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
              [33mdisabled[39m=[32m""[39m
            [36m>[39m
              [0mPrevious[0m
            [36m</button>[39m
            [36m<span[39m
              [33mclass[39m=[32m"text-sm text-muted-foreground"[39m
            [36m>[39m
              [0mPage [0m
              [0m1[0m
              [0m of [0m
              [0m1[0m
            [36m</span>[39m
            [36m<button[39m
              [33mclass[39m=[32m"px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"[39m
              [33mdisabled[39m=[32m""[39m
            [36m>[39m
              [0mNext[0m
            [36m</button>[39m
          [36m</div>[39m
        [36m</div>[39m
      [36m</div>[39m
    [36m</div>[39m
  [36m</div>[39m
[36m</body>[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m221:31[22m[39m
    [90m219| [39m    })
    [90m220| [39m
    [90m221| [39m    const editButton = screen.getByRole('button', { name: /edit user/i…
    [90m   | [39m                              [31m^[39m
    [90m222| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(editButton)
    [90m223| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[13/15]⎯[22m[39m

[41m[1m FAIL [22m[49m src/components/__tests__/UserManagement.test.tsx[2m > [22mUserManagement Component[2m > [22mcalls onBack when back button is clicked
[31m[1mTestingLibraryElementError[22m: Unable to find an accessible element with the role "button" and name `/back/i`

There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole

Ignored nodes: comments, script, style
[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"p-6"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"flex items-center justify-center h-64"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"[31m
        [36m/>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m76:38[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m52:17[22m[39m
[90m [2m❯[22m node_modules/@testing-library/dom/dist/query-helpers.js:[2m95:19[22m[39m
[36m [2m❯[22m src/components/__tests__/UserManagement.test.tsx:[2m266:31[22m[39m
    [90m264| [39m    [34mrender[39m([33m<[39m[33mUserManagement[39m [33monBack[39m[33m=[39m[33m{[39mmockOnBack[33m}[39m [33m/[39m[33m>[39m)
    [90m265| [39m    
    [90m266| [39m    [35mconst[39m backButton [33m=[39m screen[33m.[39m[34mgetByRole[39m([32m'button'[39m[33m,[39m { name[33m:[39m [36m/back/i[39m })
    [90m   | [39m                              [31m^[39m
    [90m267| [39m    [35mawait[39m user[33m.[39m[34mclick[39m(backButton)
    [90m268| [39m    

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[14/15]⎯[22m[39m

</div>
        
                </div>
            </div>
            
            <div class="section">
                <div class="section-header">⚙️ Backend Test Results</div>
                <div class="section-content">
                    <p><strong>Status:</strong> failed</p>
                    <p><strong>Return Code:</strong> 1</p>
                    
                    
            <h4>Backend Output</h4>
            <div class="test-output">Found 61 test(s).
Operations to perform:
  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles
  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions
Synchronizing apps without migrations:
  Creating tables...
    Running deferred SQL...
Running migrations:
  No migrations to apply.
System check identified no issues (0 silenced).
</div>
        
                    
            <h4>Backend Errors</h4>
            <div class="error">Using existing test database for alias 'default' ('test_th')...

test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)
Test adding user to group ... ERROR
test_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)
Test creating group ... ERROR
test_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)
Test creating group with duplicate name ... ERROR
test_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)
Test getting group details ... ERROR
test_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)
Test group operations without admin permission ... ERROR
test_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)
Test listing groups ... ERROR
test_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)
Test removing user from group ... ERROR
test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)
Test admin permissions ... ERROR
test_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)
Test user hierarchy permissions ... ERROR
test_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)
Test regular user restrictions ... ERROR
test_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)
Test superuser has all permissions ... ERROR
test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)
Test bulk activate users ... ERROR
test_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)
Test bulk delete users ... ERROR
test_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)
Test creating user as admin ... ERROR
test_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)
Test creating user with duplicate email ... ERROR
test_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)
Test creating user with invalid data ... ERROR
test_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)
Test creating user and assigning to group ... ERROR
test_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)
Test getting user details ... ERROR
test_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)
Test listing users as admin ... ERROR
test_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)
Test listing users without admin permission ... ERROR
test_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)
Test toggling user status ... ERROR
test_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)
Test updating user ... ERROR
test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)
Test that new captcha replaces old one ... ERROR
test_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)
Test that captcha answer is stored in session ... ERROR
test_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)
Test captcha generation ... ERROR
test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)
Test login with inactive user ... ERROR
test_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)
Test login with invalid captcha ... ERROR
test_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)
Test login with invalid credentials ... ERROR
test_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)
Test login with missing required fields ... ERROR
test_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)
Test successful BCE login ... ERROR
test_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)
Test successful vendor login ... ERROR
test_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)
Test that user permissions are included in login response ... ERROR
test_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)
Test BCE user trying to login via vendor ... ERROR
test_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)
Test logout without being logged in ... ERROR
test_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)
Test successful logout ... ERROR
test_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)
Test that session is cleaned up on logout ... ERROR
test_session_creation_on_login (tests.test_auth.SessionTestCase.test_session_creation_on_login)
Test that session is created on login ... ok
test_full_name_property (tests.test_auth.UserModelTestCase.test_full_name_property)
Test full_name property ... ok
test_user_creation (tests.test_auth.UserModelTestCase.test_user_creation)
Test user creation with custom fields ... ok
test_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)
Test user permission methods ... FAIL
test_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)
Test user string representation ... FAIL
test_group_creation (tests.test_models.GroupModelTestCase.test_group_creation)
Test creating groups ... ok
test_group_name_uniqueness (tests.test_models.GroupModelTestCase.test_group_name_uniqueness)
Test that group names must be unique ... ok
test_group_permissions (tests.test_models.GroupModelTestCase.test_group_permissions)
Test group permissions ... ok
test_group_user_relationship (tests.test_models.GroupModelTestCase.test_group_user_relationship)
Test group-user many-to-many relationship ... ok
test_email_uniqueness (tests.test_models.UserModelTestCase.test_email_uniqueness)
Test that email addresses must be unique ... ok
test_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)
Test full_name property ... FAIL
test_user_active_status (tests.test_models.UserModelTestCase.test_user_active_status)
Test user active status ... ok
test_user_creation_with_all_fields (tests.test_models.UserModelTestCase.test_user_creation_with_all_fields)
Test creating user with all fields ... ok
test_user_creation_with_required_fields (tests.test_models.UserModelTestCase.test_user_creation_with_required_fields)
Test creating user with required fields only ... ok
test_user_groups (tests.test_models.UserModelTestCase.test_user_groups)
Test user group relationships ... ok
test_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)
Test user permission hierarchy ... FAIL
test_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)
Test user __str__ method ... FAIL
test_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)
Test user type validation ... ERROR
test_user_creation (tests.test_simple.DatabaseTestCase.test_user_creation)
Test that we can create users ... ok
test_user_query (tests.test_simple.DatabaseTestCase.test_user_query)
Test that we can query users ... ok
test_user_relationships (tests.test_simple.DatabaseTestCase.test_user_relationships)
Test user relationships work ... ok
test_basic_math (tests.test_simple.SimpleTestCase.test_basic_math)
Test basic math operations ... ok
test_dictionary_operations (tests.test_simple.SimpleTestCase.test_dictionary_operations)
Test dictionary operations ... ok
test_list_operations (tests.test_simple.SimpleTestCase.test_list_operations)
Test list operations ... ok
test_string_operations (tests.test_simple.SimpleTestCase.test_string_operations)
Test string operations ... ok

======================================================================
ERROR: test_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group)
Test adding user to group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 246, in test_add_user_to_group
    reverse('add_user_to_group', kwargs={'group_id': self.test_group.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'add_user_to_group' not found. 'add_user_to_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_group (tests.test_admin_controls.GroupManagementTestCase.test_create_group)
Test creating group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 205, in test_create_group
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_group_duplicate_name (tests.test_admin_controls.GroupManagementTestCase.test_create_group_duplicate_name)
Test creating group with duplicate name
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 219, in test_create_group_duplicate_name
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_get_group_details (tests.test_admin_controls.GroupManagementTestCase.test_get_group_details)
Test getting group details
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 227, in test_get_group_details
    reverse('get_group_details', kwargs={'group_id': self.test_group.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'get_group_details' not found. 'get_group_details' is not a valid view function or pattern name.

======================================================================
ERROR: test_group_permissions_unauthorized (tests.test_admin_controls.GroupManagementTestCase.test_group_permissions_unauthorized)
Test group operations without admin permission
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 277, in test_group_permissions_unauthorized
    response = self.post_json(reverse('create_group'), group_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_group' not found. 'create_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_groups (tests.test_admin_controls.GroupManagementTestCase.test_list_groups)
Test listing groups
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 185, in test_list_groups
    response = self.client.get(reverse('list_groups'))
                               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_groups' not found. 'list_groups' is not a valid view function or pattern name.

======================================================================
ERROR: test_remove_user_from_group (tests.test_admin_controls.GroupManagementTestCase.test_remove_user_from_group)
Test removing user from group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 263, in test_remove_user_from_group
    reverse('remove_user_from_group', kwargs={'group_id': self.test_group.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'remove_user_from_group' not found. 'remove_user_from_group' is not a valid view function or pattern name.

======================================================================
ERROR: test_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions)
Test admin permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 304, in test_admin_permissions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_hierarchy_permissions (tests.test_admin_controls.PermissionTestCase.test_hierarchy_permissions)
Test user hierarchy permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 329, in test_hierarchy_permissions
    reverse('toggle_user_status', kwargs={'user_id': self.superuser.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.

======================================================================
ERROR: test_regular_user_restrictions (tests.test_admin_controls.PermissionTestCase.test_regular_user_restrictions)
Test regular user restrictions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 318, in test_regular_user_restrictions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_superuser_permissions (tests.test_admin_controls.PermissionTestCase.test_superuser_permissions)
Test superuser has all permissions
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 293, in test_superuser_permissions
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate)
Test bulk activate users
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 148, in test_bulk_user_action_activate
    response = self.post_json(reverse('bulk_user_action'), bulk_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.

======================================================================
ERROR: test_bulk_user_action_delete (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_delete)
Test bulk delete users
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 168, in test_bulk_user_action_delete
    response = self.post_json(reverse('bulk_user_action'), bulk_data)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'bulk_user_action' not found. 'bulk_user_action' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_as_admin (tests.test_admin_controls.UserManagementTestCase.test_create_user_as_admin)
Test creating user as admin
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 47, in test_create_user_as_admin
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_duplicate_email (tests.test_admin_controls.UserManagementTestCase.test_create_user_duplicate_email)
Test creating user with duplicate email
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 74, in test_create_user_duplicate_email
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_invalid_data (tests.test_admin_controls.UserManagementTestCase.test_create_user_invalid_data)
Test creating user with invalid data
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 82, in test_create_user_invalid_data
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_create_user_with_group (tests.test_admin_controls.UserManagementTestCase.test_create_user_with_group)
Test creating user and assigning to group
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 62, in test_create_user_with_group
    response = self.post_json(reverse('create_user'), user_data)
                              ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'create_user' not found. 'create_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_get_user_details (tests.test_admin_controls.UserManagementTestCase.test_get_user_details)
Test getting user details
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 90, in test_get_user_details
    reverse('get_user_details', kwargs={'user_id': self.regular_user.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'get_user_details' not found. 'get_user_details' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_users_as_admin (tests.test_admin_controls.UserManagementTestCase.test_list_users_as_admin)
Test listing users as admin
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 20, in test_list_users_as_admin
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_list_users_unauthorized (tests.test_admin_controls.UserManagementTestCase.test_list_users_unauthorized)
Test listing users without admin permission
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 39, in test_list_users_unauthorized
    response = self.client.get(reverse('list_users'))
                               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'list_users' not found. 'list_users' is not a valid view function or pattern name.

======================================================================
ERROR: test_toggle_user_status (tests.test_admin_controls.UserManagementTestCase.test_toggle_user_status)
Test toggling user status
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 127, in test_toggle_user_status
    reverse('toggle_user_status', kwargs={'user_id': self.regular_user.id})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'toggle_user_status' not found. 'toggle_user_status' is not a valid view function or pattern name.

======================================================================
ERROR: test_update_user (tests.test_admin_controls.UserManagementTestCase.test_update_user)
Test updating user
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_admin_controls.py", line 110, in test_update_user
    reverse('update_user', kwargs={'user_id': self.regular_user.id}),
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'update_user' not found. 'update_user' is not a valid view function or pattern name.

======================================================================
ERROR: test_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration)
Test that new captcha replaces old one
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 35, in test_captcha_regeneration
    response1 = self.client.post(reverse('generate_captcha'))
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_captcha_stored_in_session (tests.test_auth.CaptchaTestCase.test_captcha_stored_in_session)
Test that captcha answer is stored in session
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 27, in test_captcha_stored_in_session
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_generate_captcha (tests.test_auth.CaptchaTestCase.test_generate_captcha)
Test captcha generation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 18, in test_generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login)
Test login with inactive user
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 111, in test_inactive_user_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_invalid_captcha (tests.test_auth.LoginTestCase.test_invalid_captcha)
Test login with invalid captcha
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 92, in test_invalid_captcha
    self.generate_captcha()  # Generate captcha but use wrong answer
    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_invalid_credentials (tests.test_auth.LoginTestCase.test_invalid_credentials)
Test login with invalid credentials
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 76, in test_invalid_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_missing_fields (tests.test_auth.LoginTestCase.test_missing_fields)
Test login with missing required fields
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 133, in test_missing_fields
    response = self.post_json(reverse('login'), {})
                              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'login' not found. 'login' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_bce_login (tests.test_auth.LoginTestCase.test_successful_bce_login)
Test successful BCE login
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 51, in test_successful_bce_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_vendor_login (tests.test_auth.LoginTestCase.test_successful_vendor_login)
Test successful vendor login
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 64, in test_successful_vendor_login
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_user_permissions_in_response (tests.test_auth.LoginTestCase.test_user_permissions_in_response)
Test that user permissions are included in login response
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 140, in test_user_permissions_in_response
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_wrong_login_type (tests.test_auth.LoginTestCase.test_wrong_login_type)
Test BCE user trying to login via vendor
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 122, in test_wrong_login_type
    response = self.login_with_credentials(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 116, in login_with_credentials
    captcha_answer = self.generate_captcha()
                     ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\base.py", line 110, in generate_captcha
    response = self.client.post(reverse('generate_captcha'))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.

======================================================================
ERROR: test_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login)
Test logout without being logged in
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 170, in test_logout_without_login
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_successful_logout (tests.test_auth.LogoutTestCase.test_successful_logout)
Test successful logout
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 160, in test_successful_logout
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout)
Test that session is cleaned up on logout
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 193, in test_session_cleanup_on_logout
    response = self.client.post(reverse('logout'))
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'logout' not found. 'logout' is not a valid view function or pattern name.

======================================================================
ERROR: test_user_type_choices (tests.test_models.UserModelTestCase.test_user_type_choices)
Test user type validation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\psycopg\cursor.py", line 97, in execute
    raise ex.with_traceback(None)
psycopg.errors.UniqueViolation: duplicate key value violates unique constraint "accounts_user_username_key"
DETAIL:  Key (username)=(<EMAIL>) already exists.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 129, in test_user_type_choices
    user = User.objects.create_user(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\accounts\models.py", line 16, in create_user
    user.save(using=self._db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\contrib\auth\base_user.py", line 65, in save
    super().save(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\psycopg\cursor.py", line 97, in execute
    raise ex.with_traceback(None)
django.db.utils.IntegrityError: duplicate key value violates unique constraint "accounts_user_username_key"
DETAIL:  Key (username)=(<EMAIL>) already exists.

======================================================================
FAIL: test_user_permissions (tests.test_auth.UserModelTestCase.test_user_permissions)
Test user permission methods
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 226, in test_user_permissions
    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
AssertionError: False is not true

======================================================================
FAIL: test_user_string_representation (tests.test_auth.UserModelTestCase.test_user_string_representation)
Test user string representation
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_auth.py", line 240, in test_user_string_representation
    self.assertEqual(str(self.admin_user), '<EMAIL>')
AssertionError: '<EMAIL> (Admin)' != '<EMAIL>'
- <EMAIL> (Admin)
?               --------
+ <EMAIL>


======================================================================
FAIL: test_full_name_property (tests.test_models.UserModelTestCase.test_full_name_property)
Test full_name property
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 87, in test_full_name_property
    self.assertEqual(user.full_name, '<EMAIL>')
AssertionError: '' != '<EMAIL>'
+ <EMAIL>


======================================================================
FAIL: test_user_permissions_hierarchy (tests.test_models.UserModelTestCase.test_user_permissions_hierarchy)
Test user permission hierarchy
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 92, in test_user_permissions_hierarchy
    self.assertTrue(self.superuser.can_deactivate_user(self.admin_user))
AssertionError: False is not true

======================================================================
FAIL: test_user_string_representation (tests.test_models.UserModelTestCase.test_user_string_representation)
Test user __str__ method
----------------------------------------------------------------------
Traceback (most recent call last):
  File "C:\Users\<USER>\IdeaProjects\th-v3-11\backend\tests\test_models.py", line 56, in test_user_string_representation
    self.assertEqual(str(self.admin_user), '<EMAIL>')
AssertionError: '<EMAIL> (Admin)' != '<EMAIL>'
- <EMAIL> (Admin)
?               --------
+ <EMAIL>


----------------------------------------------------------------------
Ran 61 tests in 67.363s

FAILED (failures=5, errors=37)
Preserving test database for alias 'default' ('test_th')...

</div>
        
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Generated on: 2025-07-15 00:32:52
        </div>
    </div>
</body>
</html>
        