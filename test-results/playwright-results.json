{"config": {"configFile": "C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\playwright.config.ts", "rootDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/playwright-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 14, "webServer": null}, "suites": [], "errors": [{"message": "Error: Timed out waiting 120000ms from config.webServer.", "stack": "Error: Timed out waiting 120000ms from config.webServer."}], "stats": {"startTime": "2025-07-15T03:30:32.832Z", "duration": 121439.586, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}