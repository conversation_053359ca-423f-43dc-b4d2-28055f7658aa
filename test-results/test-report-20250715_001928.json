{"timestamp": "2025-07-15T00:19:28.884015", "frontend": {"status": "error", "error": "[WinError 2] The system cannot find the file specified"}, "backend": {"status": "failed", "return_code": 1, "stdout": "Found 54 test(s).\nOperations to perform:\n  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles\n  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions\nSynchronizing apps without migrations:\n  Creating tables...\n    Running deferred SQL...\nRunning migrations:\n  Applying contenttypes.0001_initial... OK\n  Applying contenttypes.0002_remove_content_type_name... OK\n  Applying auth.0001_initial... OK\n  Applying auth.0002_alter_permission_name_max_length... OK\n  Applying auth.0003_alter_user_email_max_length... OK\n  Applying auth.0004_alter_user_username_opts... OK\n  Applying auth.0005_alter_user_last_login_null... OK\n  Applying auth.0006_require_contenttypes_0002... OK\n  Applying auth.0007_alter_validators_add_error_messages... OK\n  Applying auth.0008_alter_user_username_max_length... OK\n  Applying auth.0009_alter_user_last_name_max_length... OK\n  Applying auth.0010_alter_group_name_max_length... OK\n  Applying auth.0011_update_proxy_permissions... OK\n  Applying auth.0012_alter_user_first_name_max_length... OK\n  Applying accounts.0001_initial... OK\n  Applying accounts.0002_alter_user_managers... OK\n  Applying admin.0001_initial... OK\n  Applying admin.0002_logentry_remove_auto_add... OK\n  Applying admin.0003_logentry_add_action_flag_choices... OK\n  Applying guardian.0001_initial... OK\n  Applying guardian.0002_generic_permissions_index... OK\n  Applying sessions.0001_initial... OK\nSystem check identified no issues (0 silenced).\n\n\ntest_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login) failed:\n\n    NoReverseMatch(\"Reverse for 'generate_captcha' not found.\n    'generate_captcha' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_captcha_regeneration (tests.test_auth.CaptchaTestCase.test_captcha_regeneration) failed:\n\n    NoReverseMatch(\"Reverse for 'generate_captcha' not found.\n    'generate_captcha' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_logout_without_login (tests.test_auth.LogoutTestCase.test_logout_without_login) failed:\n\n    NoReverseMatch(\"Reverse for 'logout' not found. 'logout' is not a valid\n    view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate) failed:\n\n    NoReverseMatch(\"Reverse for 'bulk_user_action' not found.\n    'bulk_user_action' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout) failed:\n\n    NoReverseMatch(\"Reverse for 'logout' not found. 'logout' is not a valid\n    view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group) failed:\n\n    NoReverseMatch(\"Reverse for 'add_user_to_group' not found.\n    'add_user_to_group' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n", "stderr": "Using existing test database for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nPreserving test database for alias 'default' ('test_th_1')...\n\nPreserving test database for alias 'default' ('test_th_2')...\n\nPreserving test database for alias 'default' ('test_th_3')...\n\nPreserving test database for alias 'default' ('test_th_4')...\n\nPreserving test database for alias 'default' ('test_th_5')...\n\nPreserving test database for alias 'default' ('test_th_6')...\n\nPreserving test database for alias 'default' ('test_th_7')...\n\nPreserving test database for alias 'default' ('test_th_8')...\n\nPreserving test database for alias 'default' ('test_th_9')...\n\nPreserving test database for alias 'default' ('test_th_10')...\n\nPreserving test database for alias 'default' ('test_th')...\n\nmultiprocessing.pool.RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 58, in testPartExecutor\n    yield\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 634, in run\n    self._callTestMethod(testMethod)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 589, in _callTestMethod\n    if method() is not None:\n       ^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_auth.py\", line 111, in test_inactive_user_login\n    response = self.login_with_credentials(\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 116, in login_with_credentials\n    captcha_answer = self.generate_captcha()\n                     ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\base.py\", line 110, in generate_captcha\n    response = self.client.post(reverse('generate_captcha'))\n                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'generate_captcha' not found. 'generate_captcha' is not a valid view function or pattern name.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\pool.py\", line 125, in worker\n    result = (True, func(*args, **kwds))\n                    ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 466, in _run_subsuite\n    result = runner.run(subsuite)\n             ^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 381, in run\n    test(result)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\suite.py\", line 84, in __call__\n    return self.run(*args, **kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\suite.py\", line 122, in run\n    test(result)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\testcases.py\", line 321, in __call__\n    self._setup_and_call(result)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\testcases.py\", line 376, in _setup_and_call\n    super().__call__(result)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 690, in __call__\n    return self.run(*args, **kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 633, in run\n    with outcome.testPartExecutor(self):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py\", line 155, in __exit__\n    self.gen.throw(value)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 75, in testPartExecutor\n    _addError(self.result, test_case, exc_info)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py\", line 100, in _addError\n    result.addError(test, exc_info)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 298, in addError\n    self.check_picklable(test, err)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 219, in check_picklable\n    self._confirm_picklable(err)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 189, in _confirm_picklable\n    pickle.loads(pickle.dumps(obj))\n                 ^^^^^^^^^^^^^^^^^\nTypeError: cannot pickle 'traceback' object\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\manage.py\", line 22, in <module>\n    main()\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\manage.py\", line 18, in main\n    execute_from_command_line(sys.argv)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\__init__.py\", line 442, in execute_from_command_line\n    utility.execute()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\__init__.py\", line 436, in execute\n    self.fetch_command(subcommand).run_from_argv(self.argv)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\commands\\test.py\", line 24, in run_from_argv\n    super().run_from_argv(argv)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\base.py\", line 416, in run_from_argv\n    self.execute(*args, **cmd_options)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\base.py\", line 460, in execute\n    output = self.handle(*args, **options)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\core\\management\\commands\\test.py\", line 63, in handle\n    failures = test_runner.run_tests(test_labels)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 1099, in run_tests\n    result = self.run_suite(suite)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 1026, in run_suite\n    return runner.run(suite)\n           ^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\runner.py\", line 240, in run\n    test(result)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\suite.py\", line 84, in __call__\n    return self.run(*args, **kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\django\\test\\runner.py\", line 553, in run\n    subsuite_index, events = test_results.next(timeout=0.1)\n                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\pool.py\", line 873, in next\n    raise value\nTypeError: cannot pickle 'traceback' object\n"}, "summary": {"overall_status": "failed", "frontend_status": "error", "backend_status": "failed", "timestamp": "2025-07-15T00:19:28.884015"}}