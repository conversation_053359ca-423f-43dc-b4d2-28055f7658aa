{"timestamp": "2025-07-15T08:52:04.620372", "frontend": {"status": "failed", "return_code": 1, "test_results": {"numTotalTestSuites": 12, "numPassedTestSuites": 2, "numFailedTestSuites": 10, "numPendingTestSuites": 0, "numTotalTests": 16, "numPassedTests": 5, "numFailedTests": 11, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752549725054, "success": false, "testResults": [{"assertionResults": [], "startTime": 1752549725054, "endTime": 1752549725054, "status": "failed", "message": "Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e/admin.spec.ts"}, {"assertionResults": [], "startTime": 1752549725054, "endTime": 1752549725054, "status": "failed", "message": "Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e/auth.spec.ts"}, {"assertionResults": [], "startTime": 1752549725054, "endTime": 1752549725054, "status": "failed", "message": "Playwright Test did not expect test.describe() to be called here.\nMost common reasons include:\n- You are calling test.describe() in a configuration file.\n- You are calling test.describe() in a file that is imported by the configuration file.\n- You have two different versions of @playwright/test. This usually happens\n  when one of the dependencies in your package.json depends on @playwright/test.", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/tests/e2e/user-workflow.spec.ts"}, {"assertionResults": [{"ancestorTitles": ["App Component"], "fullName": "App Component renders without crashing", "status": "failed", "title": "renders without crashing", "duration": 15.011600000000044, "failureMessages": ["Error: You cannot render a <Router> inside another <Router>. You should never have more than one in your app.\n    at invariant (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:188:11)\n    at Router (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:5746:3)\n    at Object.react-stack-bottom-frame (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:23863:20)\n    at renderWithHooks (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:5529:22)\n    at updateFunctionComponent (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:8897:19)\n    at beginWork (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:10522:18)\n    at runWithFiberInDEV (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:1522:13)\n    at performUnitOfWork (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:15140:22)\n    at workLoopSync (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:14956:41)\n    at renderRootSync (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:14936:11)"], "meta": {}}, {"ancestorTitles": ["App Component"], "fullName": "App Component handles routing correctly", "status": "failed", "title": "handles routing correctly", "duration": 2.**************, "failureMessages": ["Error: You cannot render a <Router> inside another <Router>. You should never have more than one in your app.\n    at invariant (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:188:11)\n    at Router (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/react-router/dist/development/chunk-QMGIS6GS.mjs:5746:3)\n    at Object.react-stack-bottom-frame (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:23863:20)\n    at renderWithHooks (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:5529:22)\n    at updateFunctionComponent (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:8897:19)\n    at beginWork (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:10522:18)\n    at runWithFiberInDEV (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:1522:13)\n    at performUnitOfWork (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:15140:22)\n    at workLoopSync (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:14956:41)\n    at renderRootSync (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\react-dom\\cjs\\react-dom-client.development.js:14936:11)"], "meta": {}}], "startTime": 1752549726308, "endTime": 1752549726325.0906, "status": "failed", "message": "", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/App.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["GroupManagement Component"], "fullName": "GroupManagement Component renders group management interface", "status": "failed", "title": "renders group management interface", "duration": 22.526499999999942, "failureMessages": ["TestingLibraryElementError: Unable to find an element with the text: Group Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"text-muted-foreground\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\GroupManagement.test.tsx:30:19\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["GroupManagement Component"], "fullName": "GroupManagement Component has search functionality", "status": "failed", "title": "has search functionality", "duration": 3.7194999999999254, "failureMessages": ["TestingLibraryElementError: Unable to find an element with the placeholder text of: /search groups/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"text-muted-foreground\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\GroupManagement.test.tsx:38:19\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["GroupManagement Component"], "fullName": "GroupManagement Component opens create group modal when add group button is clicked", "status": "failed", "title": "opens create group modal when add group button is clicked", "duration": 26.075599999999895, "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/add group/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"text-muted-foreground\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\GroupManagement.test.tsx:46:30\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["GroupManagement Component"], "fullName": "GroupManagement Component has back button functionality", "status": "failed", "title": "has back button functionality", "duration": 4.804499999999962, "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"text-muted-foreground\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[0mLoading groups...\u001b[0m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\GroupManagement.test.tsx:56:31\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}], "startTime": 1752549726282, "endTime": 1752549726339.8044, "status": "failed", "message": "", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/GroupManagement.test.tsx"}, {"assertionResults": [], "startTime": 1752549725054, "endTime": 1752549725054, "status": "failed", "message": "Failed to resolve import \"../Login\" from \"src/components/__tests__/Login.test.tsx\". Does the file exist?", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/Login.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["Simple Test Suite"], "fullName": "Simple Test Suite should pass basic math test", "status": "passed", "title": "should pass basic math test", "duration": 0.8808999999999969, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Test Suite"], "fullName": "Simple Test Suite should pass string test", "status": "passed", "title": "should pass string test", "duration": 0.1792000000000371, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Test Suite"], "fullName": "Simple Test Suite should pass array test", "status": "passed", "title": "should pass array test", "duration": 0.6916999999999689, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Test Suite"], "fullName": "Simple Test Suite should pass object test", "status": "passed", "title": "should pass object test", "duration": 0.23109999999996944, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Simple Test Suite"], "fullName": "Simple Test Suite should pass async test", "status": "passed", "title": "should pass async test", "duration": 0.12810000000001764, "failureMessages": [], "meta": {}}], "startTime": 1752549726088, "endTime": 1752549726090.2312, "status": "passed", "message": "", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/Simple.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["UserManagement Component"], "fullName": "UserManagement Component renders user management interface", "status": "failed", "title": "renders user management interface", "duration": 22.391900000000078, "failureMessages": ["TestingLibraryElementError: Unable to find an element with the text: User Management. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"p-6\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\UserManagement.test.tsx:30:19\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["UserManagement Component"], "fullName": "UserManagement Component has search functionality", "status": "failed", "title": "has search functionality", "duration": 4.033099999999877, "failureMessages": ["TestingLibraryElementError: Unable to find an element with the placeholder text of: /search users/i\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"p-6\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\UserManagement.test.tsx:38:19\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["UserManagement Component"], "fullName": "UserManagement Component opens create user modal when add user button is clicked", "status": "failed", "title": "opens create user modal when add user button is clicked", "duration": 29.55469999999991, "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/add user/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"p-6\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\UserManagement.test.tsx:46:30\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}, {"ancestorTitles": ["UserManagement Component"], "fullName": "UserManagement Component has bulk operations interface", "status": "failed", "title": "has bulk operations interface", "duration": 5.429800000000114, "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"checkbox\"\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"p-6\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:109:15\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\UserManagement.test.tsx:56:31\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)"], "meta": {}}, {"ancestorTitles": ["UserManagement Component"], "fullName": "UserManagement Component has back button functionality", "status": "failed", "title": "has back button functionality", "duration": 4.841499999999996, "failureMessages": ["TestingLibraryElementError: Unable to find an accessible element with the role \"button\" and name `/back/i`\n\nThere are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div>\u001b[39m\n    \u001b[36m<div\u001b[39m\n      \u001b[33mclass\u001b[39m=\u001b[32m\"p-6\"\u001b[39m\n    \u001b[36m>\u001b[39m\n      \u001b[36m<div\u001b[39m\n        \u001b[33mclass\u001b[39m=\u001b[32m\"flex items-center justify-center h-64\"\u001b[39m\n      \u001b[36m>\u001b[39m\n        \u001b[36m<div\u001b[39m\n          \u001b[33mclass\u001b[39m=\u001b[32m\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\u001b[39m\n        \u001b[36m/>\u001b[39m\n      \u001b[36m</div>\u001b[39m\n    \u001b[36m</div>\u001b[39m\n  \u001b[36m</div>\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at Object.getElementError (C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\config.js:37:19)\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:76:38\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:52:17\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:95:19\n    at C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\src\\components\\__tests__\\UserManagement.test.tsx:64:31\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///C:/Users/<USER>/IdeaProjects/th-v3-11/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "meta": {}}], "startTime": 1752549726306, "endTime": 1752549726371.8416, "status": "failed", "message": "", "name": "C:/Users/<USER>/IdeaProjects/th-v3-11/src/components/__tests__/UserManagement.test.tsx"}]}, "stdout": "\n> th-v3-11@0.0.0 test:run\n> vitest run --reporter=json --outputFile=C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\test-results/frontend-results.json\n\nJSON report written to C:/Users/<USER>/IdeaProjects/th-v3-11/test-results/frontend-results.json\n", "stderr": ""}, "backend": {"status": "failed", "return_code": 1, "stdout": "Found 61 test(s).\nOperations to perform:\n  Synchronize unmigrated apps: corsheaders, django_extensions, messages, rest_framework, staticfiles\n  Apply all migrations: accounts, admin, auth, contenttypes, guardian, sessions\nSynchronizing apps without migrations:\n  Creating tables...\n    Running deferred SQL...\nRunning migrations:\n  No migrations to apply.\nSystem check identified no issues (0 silenced).\n\n\ntest_add_user_to_group (tests.test_admin_controls.GroupManagementTestCase.test_add_user_to_group) failed:\n\n    NoReverseMatch(\"Reverse for 'add_user_to_group' not found.\n    'add_user_to_group' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_bulk_user_action_activate (tests.test_admin_controls.UserManagementTestCase.test_bulk_user_action_activate) failed:\n\n    NoReverseMatch(\"Reverse for 'bulk_user_action' not found.\n    'bulk_user_action' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_admin_permissions (tests.test_admin_controls.PermissionTestCase.test_admin_permissions) failed:\n\n    NoReverseMatch(\"Reverse for 'list_users' not found. 'list_users' is not\n    a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_inactive_user_login (tests.test_auth.LoginTestCase.test_inactive_user_login) failed:\n\n    NoReverseMatch(\"Reverse for 'generate_captcha' not found.\n    'generate_captcha' is not a valid view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n\n\ntest_session_cleanup_on_logout (tests.test_auth.SessionTestCase.test_session_cleanup_on_logout) failed:\n\n    NoReverseMatch(\"Reverse for 'logout' not found. 'logout' is not a valid\n    view function or pattern name.\")\n\nUnfortunately, tracebacks cannot be pickled, making it impossible for the\nparallel test runner to handle this exception cleanly.\n\nIn order to see the traceback, you should install tblib:\n\n    python -m pip install tblib\n\n", "stderr": "Using existing test database for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\nUsing existing clone for alias 'default' ('test_th')...\n\ntest_basic_math (tests.test_simple.SimpleTestCase.test_basic_math)\nTest basic math operations ... ok\ntest_dictionary_operations (tests.test_simple.SimpleTestCase.test_dictionary_operations)\nTest dictionary operations ... ok\ntest_list_operations (tests.test_simple.SimpleTestCase.test_list_operations)\nTest list operations ... ok\ntest_string_operations (tests.test_simple.SimpleTestCase.test_string_operations)\nTest string operations ... ok\nPreserving test database for alias 'default' ('test_th_1')...\n\nPreserving test database for alias 'default' ('test_th_2')...\n\nPreserving test database for alias 'default' ('test_th_3')...\n\nPreserving test database for alias 'default' ('test_th_4')...\n\nPreserving test database for alias 'default' ('test_th_5')...\n\nPreserving test database for alias 'default' ('test_th_6')...\n\nPreserving test database for alias 'default' ('test_th_7')...\n\nPreserving test database for alias 'default' ('test_th_8')...\n\nPreserving test database for alias 'default' ('test_th_9')...\n\nPreserving test database for alias 'default' ('test_th_10')...\n\nPreserving test database for alias 'default' ('test_th_11')...\n\nPreserving test database for alias 'default' ('test_th_12')...\n\nPreserving test database for alias 'default' ('test_th')...\n\nmultiprocessing.pool.RemoteTraceback: \n\"\"\"\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 58, in testPartExecutor\n    yield\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 651, in run\n    self._callTestMethod(testMethod)\n    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 606, in _callTestMethod\n    if method() is not None:\n       ~~~~~~^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\tests\\test_admin_controls.py\", line 246, in test_add_user_to_group\n    reverse('add_user_to_group', kwargs={'group_id': self.test_group.id}),\n    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\urls\\base.py\", line 98, in reverse\n    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\urls\\resolvers.py\", line 831, in _reverse_with_prefix\n    raise NoReverseMatch(msg)\ndjango.urls.exceptions.NoReverseMatch: Reverse for 'add_user_to_group' not found. 'add_user_to_group' is not a valid view function or pattern name.\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py\", line 125, in worker\n    result = (True, func(*args, **kwds))\n                    ~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 466, in _run_subsuite\n    result = runner.run(subsuite)\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 381, in run\n    test(result)\n    ~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py\", line 84, in __call__\n    return self.run(*args, **kwds)\n           ~~~~~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py\", line 122, in run\n    test(result)\n    ~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\testcases.py\", line 321, in __call__\n    self._setup_and_call(result)\n    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\testcases.py\", line 376, in _setup_and_call\n    super().__call__(result)\n    ~~~~~~~~~~~~~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 707, in __call__\n    return self.run(*args, **kwds)\n           ~~~~~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 650, in run\n    with outcome.testPartExecutor(self):\n         ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py\", line 162, in __exit__\n    self.gen.throw(value)\n    ~~~~~~~~~~~~~~^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 75, in testPartExecutor\n    _addError(self.result, test_case, exc_info)\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py\", line 100, in _addError\n    result.addError(test, exc_info)\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 298, in addError\n    self.check_picklable(test, err)\n    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 219, in check_picklable\n    self._confirm_picklable(err)\n    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 189, in _confirm_picklable\n    pickle.loads(pickle.dumps(obj))\n                 ~~~~~~~~~~~~^^^^^\nTypeError: cannot pickle 'traceback' object\n\"\"\"\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\manage.py\", line 22, in <module>\n    main()\n    ~~~~^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\th-v3-11\\backend\\manage.py\", line 18, in main\n    execute_from_command_line(sys.argv)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\__init__.py\", line 442, in execute_from_command_line\n    utility.execute()\n    ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\__init__.py\", line 436, in execute\n    self.fetch_command(subcommand).run_from_argv(self.argv)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\test.py\", line 24, in run_from_argv\n    super().run_from_argv(argv)\n    ~~~~~~~~~~~~~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\base.py\", line 416, in run_from_argv\n    self.execute(*args, **cmd_options)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\base.py\", line 460, in execute\n    output = self.handle(*args, **options)\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\test.py\", line 63, in handle\n    failures = test_runner.run_tests(test_labels)\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 1099, in run_tests\n    result = self.run_suite(suite)\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 1026, in run_suite\n    return runner.run(suite)\n           ~~~~~~~~~~^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py\", line 240, in run\n    test(result)\n    ~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py\", line 84, in __call__\n    return self.run(*args, **kwds)\n           ~~~~~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\IdeaProjects\\untitled\\.venv\\Lib\\site-packages\\django\\test\\runner.py\", line 553, in run\n    subsuite_index, events = test_results.next(timeout=0.1)\n                             ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py\", line 873, in next\n    raise value\nTypeError: cannot pickle 'traceback' object\n"}, "summary": {"overall_status": "failed", "frontend_status": "failed", "backend_status": "failed", "timestamp": "2025-07-15T08:52:04.620372"}}