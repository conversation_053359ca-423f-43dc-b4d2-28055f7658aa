import { test, expect } from '@playwright/test';
import { TestHelpers } from './utils/test-helpers';

test.describe('Complete User Workflow', () => {
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
  });

  test('Admin can manage users end-to-end', async ({ page }) => {
    // Setup mocks
    await helpers.mockUsersAPI();
    
    // Login as admin
    await helpers.loginAsAdmin();
    
    // Navigate to user management
    await helpers.navigateToUserManagement();
    
    // Verify user list is displayed
    await expect(page.locator('text=Test User 1')).toBeVisible();
    await expect(page.locator('text=Test User 2')).toBeVisible();
    
    // Test search functionality
    await page.locator('input[placeholder*="search" i]').fill('user1');
    await page.locator('button:has-text("Search")').click();
    
    // Test create user modal
    await page.locator('button:has-text("Add User")').click();
    await expect(page.locator('text=Create New User')).toBeVisible();
    
    // Close modal
    await page.locator('button:has-text("Cancel")').click();
    
    // Test bulk operations
    const checkboxes = page.locator('input[type="checkbox"]');
    await checkboxes.nth(1).click(); // Select first user
    
    await expect(page.locator('text=/1 user.* selected/')).toBeVisible();
    
    // Test bulk action dropdown
    await page.locator('select').selectOption('activate');
    await page.locator('button:has-text("Apply")').click();
  });

  test('Admin can manage groups end-to-end', async ({ page }) => {
    // Setup mocks
    await helpers.mockGroupsAPI();
    
    // Login as admin
    await helpers.loginAsAdmin();
    
    // Navigate to group management
    await helpers.navigateToGroupManagement();
    
    // Verify group list is displayed
    await expect(page.locator('text=Administrators')).toBeVisible();
    await expect(page.locator('text=Users')).toBeVisible();
    
    // Test create group
    await page.locator('button:has-text("Add Group")').click();
    await expect(page.locator('text=Add New Group')).toBeVisible();
    
    await page.locator('input[placeholder*="group name" i]').fill('Test Group');
    await page.locator('button:has-text("Create Group")').click();
    
    // Test group details
    await page.locator('button:has-text("Administrators")').click();
    await expect(page.locator('text=Members')).toBeVisible();
    await expect(page.locator('text=Permissions')).toBeVisible();
  });

  test('Regular user has limited access', async ({ page }) => {
    // Login as regular user
    await helpers.loginAsUser();
    
    // Should not see admin panel
    await expect(page.locator('text=Admin Panel')).not.toBeVisible();
    
    // Should see basic dashboard
    await expect(page.locator('h1')).toContainText('Dashboard');
  });

  test('Error handling works correctly', async ({ page }) => {
    // Mock API error
    await helpers.mockAPIError('**/api/admin-controls/users/**', 403, 'Permission denied');
    
    // Login as admin
    await helpers.loginAsAdmin();
    
    // Try to navigate to user management
    await helpers.navigateToUserManagement();
    
    // Should show error message
    await expect(page.locator('text=/permission denied/i')).toBeVisible();
  });

  test('Responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Setup mocks
    await helpers.mockUsersAPI();
    
    // Login as admin
    await helpers.loginAsAdmin();
    
    // Navigate to user management
    await helpers.navigateToUserManagement();
    
    // Should still be functional on mobile
    await expect(page.locator('h2')).toContainText('User Management');
    await expect(page.locator('button:has-text("Add User")')).toBeVisible();
  });

  test('Logout functionality works', async ({ page }) => {
    // Mock logout API
    await page.route('**/api/auth/logout/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Logged out successfully'
        })
      });
    });

    // Login as admin
    await helpers.loginAsAdmin();
    
    // Find and click logout button
    const logoutButton = page.locator('button:has-text("Logout")').or(
      page.locator('button:has-text("Sign Out")')
    ).or(
      page.locator('[aria-label*="logout" i]')
    ).first();
    
    await logoutButton.click();
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/bce/);
    await expect(page.locator('h1')).toContainText('BCE Login');
  });
});

test.describe('Form Validation', () => {
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
    await helpers.mockUsersAPI();
    await helpers.loginAsAdmin();
    await helpers.navigateToUserManagement();
  });

  test('Create user form validates required fields', async ({ page }) => {
    await page.locator('button:has-text("Add User")').click();
    
    // Try to submit without filling required fields
    await page.locator('button:has-text("Create User")').click();
    
    // Should show validation messages or prevent submission
    const emailInput = page.locator('input[placeholder*="email" i]');
    const passwordInput = page.locator('input[placeholder*="password" i]');
    
    // Form should still be open (not submitted)
    await expect(page.locator('text=Create New User')).toBeVisible();
  });

  test('Create user form validates email format', async ({ page }) => {
    await page.locator('button:has-text("Add User")').click();
    
    // Fill invalid email
    await page.locator('input[placeholder*="email" i]').fill('invalid-email');
    await page.locator('input[placeholder*="password" i]').fill('password123');
    await page.locator('input[placeholder*="first name" i]').fill('Test');
    await page.locator('input[placeholder*="last name" i]').fill('User');
    
    await page.locator('button:has-text("Create User")').click();
    
    // Should show validation error or prevent submission
    await expect(page.locator('text=Create New User')).toBeVisible();
  });
});

test.describe('Performance and Accessibility', () => {
  let helpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    helpers = new TestHelpers(page);
  });

  test('Login page loads quickly', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/bce');
    await page.waitForSelector('h1:has-text("BCE Login")');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(3000); // Should load within 3 seconds
  });

  test('Dashboard loads quickly after login', async ({ page }) => {
    await helpers.loginAsAdmin();
    
    const startTime = Date.now();
    await page.waitForSelector('h1:has-text("Dashboard")');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(2000); // Should load within 2 seconds
  });

  test('Forms are keyboard accessible', async ({ page }) => {
    await page.goto('/bce');
    
    // Tab through form elements
    await page.keyboard.press('Tab'); // Email input
    await expect(page.locator('input[placeholder*="email" i]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Password input
    await expect(page.locator('input[placeholder*="password" i]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Captcha input
    await expect(page.locator('input[placeholder*="captcha" i]')).toBeFocused();
  });
});
