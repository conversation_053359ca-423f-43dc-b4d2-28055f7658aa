import { test, expect } from '@playwright/test';

// Helper function to login as admin
async function loginAsAdmin(page: any) {
  await page.goto('/bce');
  
  // Fill login form (you'll need to adjust with actual admin credentials)
  await page.locator('input[placeholder*="email" i]').fill('<EMAIL>');
  await page.locator('input[placeholder*="password" i]').fill('admin123');
  
  // Wait for captcha and solve it
  await page.waitForSelector('text=/\\d+ [+\\-*] \\d+ = \\?/', { timeout: 10000 });
  const captchaText = await page.locator('text=/\\d+ [+\\-*] \\d+ = \\?/').textContent();
  
  if (captchaText) {
    const answer = eval(captchaText.replace(' = ?', ''));
    await page.locator('input[placeholder*="captcha" i]').fill(answer.toString());
  }

  await page.locator('button:has-text("Sign In")').click();
  
  // Wait for dashboard to load
  await page.waitForURL('**/dashboard', { timeout: 10000 });
}

test.describe('Admin Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Mock successful login response
    await page.route('**/api/auth/login/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: '1',
            email: '<EMAIL>',
            full_name: 'Admin User',
            user_type: 'admin'
          },
          permissions: {
            can_access_admin: true,
            can_create_users: true,
            can_manage_groups: true
          }
        })
      });
    });

    // Mock captcha generation
    await page.route('**/api/auth/generate-captcha/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          question: '2 + 2 = ?',
          answer: '4'
        })
      });
    });

    await loginAsAdmin(page);
  });

  test('should display dashboard with admin options', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard');
    
    // Check for admin panel access
    await expect(page.locator('text=Admin Panel')).toBeVisible();
  });

  test('should navigate to admin panel', async ({ page }) => {
    await page.locator('text=Admin Panel').click();
    
    // Should show admin panel options
    await expect(page.locator('text=User Management')).toBeVisible();
    await expect(page.locator('text=Groups & Permissions')).toBeVisible();
  });
});

test.describe('User Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/auth/login/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: { id: '1', email: '<EMAIL>', user_type: 'admin' },
          permissions: { can_access_admin: true, can_create_users: true }
        })
      });
    });

    await page.route('**/api/auth/generate-captcha/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, question: '2 + 2 = ?', answer: '4' })
      });
    });

    await page.route('**/api/admin-controls/users/**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            users: [
              {
                id: '1',
                email: '<EMAIL>',
                full_name: 'Test User',
                user_type: 'user',
                is_active: true,
                can_deactivate: true,
                groups: []
              }
            ],
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 1,
              has_next: false,
              has_previous: false
            }
          }
        })
      });
    });

    await loginAsAdmin(page);
    await page.locator('text=Admin Panel').click();
    await page.locator('text=User Management').click();
  });

  test('should display user management interface', async ({ page }) => {
    await expect(page.locator('h2')).toContainText('User Management');
    await expect(page.locator('button:has-text("Add User")')).toBeVisible();
    await expect(page.locator('input[placeholder*="search" i]')).toBeVisible();
  });

  test('should open create user modal', async ({ page }) => {
    await page.locator('button:has-text("Add User")').click();
    
    await expect(page.locator('text=Create New User')).toBeVisible();
    await expect(page.locator('input[placeholder*="email" i]')).toBeVisible();
    await expect(page.locator('input[placeholder*="password" i]')).toBeVisible();
  });

  test('should display user list', async ({ page }) => {
    // Should show the mocked user
    await expect(page.locator('text=Test User')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should have bulk operations', async ({ page }) => {
    // Check for checkboxes
    const checkboxes = page.locator('input[type="checkbox"]');
    await expect(checkboxes.first()).toBeVisible();
    
    // Select a user
    await checkboxes.nth(1).click(); // Skip header checkbox
    
    // Should show bulk actions
    await expect(page.locator('text=/\d+ user.* selected/')).toBeVisible();
  });
});

test.describe('Group Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/auth/login/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: { id: '1', email: '<EMAIL>', user_type: 'admin' },
          permissions: { can_access_admin: true, can_manage_groups: true }
        })
      });
    });

    await page.route('**/api/auth/generate-captcha/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, question: '2 + 2 = ?', answer: '4' })
      });
    });

    await page.route('**/api/admin-controls/groups/**', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              groups: [
                {
                  id: 1,
                  name: 'Test Group',
                  permissions_count: 5,
                  users_count: 3
                }
              ],
              pagination: {
                current_page: 1,
                total_pages: 1,
                total_count: 1,
                has_next: false,
                has_previous: false
              }
            }
          })
        });
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await loginAsAdmin(page);
    await page.locator('text=Admin Panel').click();
    await page.locator('text=Groups & Permissions').click();
  });

  test('should display group management interface', async ({ page }) => {
    await expect(page.locator('h2')).toContainText('Group Management');
    await expect(page.locator('button:has-text("Add Group")')).toBeVisible();
    await expect(page.locator('input[placeholder*="search" i]')).toBeVisible();
  });

  test('should open create group modal', async ({ page }) => {
    await page.locator('button:has-text("Add Group")').click();
    
    await expect(page.locator('text=Add New Group')).toBeVisible();
    await expect(page.locator('input[placeholder*="group name" i]')).toBeVisible();
  });

  test('should display group list', async ({ page }) => {
    await expect(page.locator('text=Test Group')).toBeVisible();
    await expect(page.locator('text=5 permissions')).toBeVisible();
    await expect(page.locator('text=3 users')).toBeVisible();
  });

  test('should open group details when clicked', async ({ page }) => {
    // Mock group details response
    await page.route('**/api/admin-controls/groups/1/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          group: {
            id: 1,
            name: 'Test Group',
            members: [
              {
                id: '1',
                email: '<EMAIL>',
                full_name: 'Test User',
                user_type: 'user',
                is_active: true
              }
            ],
            permissions: [],
            members_count: 1,
            permissions_count: 0
          }
        })
      });
    });

    await page.locator('button:has-text("Test Group")').click();
    
    await expect(page.locator('text=Test Group')).toBeVisible();
    await expect(page.locator('text=Members')).toBeVisible();
    await expect(page.locator('text=Permissions')).toBeVisible();
  });

  test('should show add member functionality', async ({ page }) => {
    // Mock group details and users list
    await page.route('**/api/admin-controls/groups/1/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          group: {
            id: 1,
            name: 'Test Group',
            members: [],
            permissions: [],
            members_count: 0,
            permissions_count: 0
          }
        })
      });
    });

    await page.route('**/api/admin-controls/users/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            users: [
              {
                id: '1',
                email: '<EMAIL>',
                full_name: 'Test User',
                is_active: true
              }
            ]
          }
        })
      });
    });

    await page.locator('button:has-text("Test Group")').click();
    await page.locator('button:has-text("Add Member")').click();
    
    await expect(page.locator('text=Add Member to Test Group')).toBeVisible();
    await expect(page.locator('select')).toBeVisible();
  });
});
