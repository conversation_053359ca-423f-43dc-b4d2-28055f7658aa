import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the BCE login page
    await page.goto('/bce');
  });

  test('should display BCE login form', async ({ page }) => {
    // Check if the login form is displayed
    await expect(page.locator('h1')).toContainText('BCE Login');
    await expect(page.locator('input[placeholder*="email" i]')).toBeVisible();
    await expect(page.locator('input[placeholder*="password" i]')).toBeVisible();
    await expect(page.locator('button:has-text("Sign In")')).toBeVisible();
  });

  test('should display captcha', async ({ page }) => {
    // Wait for captcha to load
    await page.waitForSelector('text=/\\d+ [+\\-*] \\d+ = \\?/', { timeout: 10000 });
    
    // Check if captcha question is displayed
    const captchaText = await page.locator('text=/\\d+ [+\\-*] \\d+ = \\?/').textContent();
    expect(captchaText).toMatch(/\d+ [+\-*] \d+ = \?/);
    
    // Check if captcha input is present
    await expect(page.locator('input[placeholder*="captcha" i]')).toBeVisible();
  });

  test('should show password toggle functionality', async ({ page }) => {
    const passwordInput = page.locator('input[placeholder*="password" i]');
    const toggleButton = page.locator('button').filter({ hasText: /show|hide/i }).or(
      page.locator('button[aria-label*="password"]')
    ).or(
      page.locator('button:has(svg)')
    ).first();

    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password');

    // Click toggle button to show password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'text');

    // Click again to hide password
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should refresh captcha when refresh button is clicked', async ({ page }) => {
    // Wait for initial captcha
    await page.waitForSelector('text=/\\d+ [+\\-*] \\d+ = \\?/', { timeout: 10000 });
    const initialCaptcha = await page.locator('text=/\\d+ [+\\-*] \\d+ = \\?/').textContent();

    // Find and click refresh button
    const refreshButton = page.locator('button[title*="refresh" i]').or(
      page.locator('button:has(svg)').filter({ hasText: '' })
    ).first();
    
    await refreshButton.click();

    // Wait for captcha to change
    await page.waitForTimeout(1000);
    const newCaptcha = await page.locator('text=/\\d+ [+\\-*] \\d+ = \\?/').textContent();
    
    // Captcha should be different (though there's a small chance it could be the same)
    expect(newCaptcha).toMatch(/\d+ [+\-*] \d+ = \?/);
  });

  test('should validate required fields', async ({ page }) => {
    // Try to submit without filling fields
    await page.locator('button:has-text("Sign In")').click();
    
    // Should show validation messages or prevent submission
    // This depends on your actual validation implementation
    const emailInput = page.locator('input[placeholder*="email" i]');
    const passwordInput = page.locator('input[placeholder*="password" i]');
    
    // Check if inputs are still focused or show validation
    await expect(emailInput).toBeFocused().or(expect(passwordInput).toBeFocused());
  });

  test('should handle form submission', async ({ page }) => {
    // Fill in the form
    await page.locator('input[placeholder*="email" i]').fill('<EMAIL>');
    await page.locator('input[placeholder*="password" i]').fill('testpassword');
    
    // Wait for captcha and solve it
    await page.waitForSelector('text=/\\d+ [+\\-*] \\d+ = \\?/', { timeout: 10000 });
    const captchaText = await page.locator('text=/\\d+ [+\\-*] \\d+ = \\?/').textContent();
    
    if (captchaText) {
      // Simple math evaluation for captcha
      const answer = eval(captchaText.replace(' = ?', ''));
      await page.locator('input[placeholder*="captcha" i]').fill(answer.toString());
    }

    // Submit the form
    await page.locator('button:has-text("Sign In")').click();
    
    // Should either redirect on success or show error message
    // Wait for either dashboard or error message
    await Promise.race([
      page.waitForURL('**/dashboard', { timeout: 5000 }).catch(() => {}),
      page.waitForSelector('text=/invalid|error|failed/i', { timeout: 5000 }).catch(() => {})
    ]);
  });
});

test.describe('Vendor Login', () => {
  test('should display vendor login form', async ({ page }) => {
    await page.goto('/vendor');
    
    await expect(page.locator('h1')).toContainText('Vendor Login');
    await expect(page.locator('input[placeholder*="email" i]')).toBeVisible();
    await expect(page.locator('input[placeholder*="password" i]')).toBeVisible();
    await expect(page.locator('button:has-text("Sign In")')).toBeVisible();
  });
});

test.describe('Navigation', () => {
  test('should redirect root to BCE login', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveURL(/.*\/bce/);
  });

  test('should handle invalid routes', async ({ page }) => {
    await page.goto('/invalid-route');
    // Should either redirect to login or show 404
    await expect(page).toHaveURL(/.*\/(bce|404|not-found)/);
  });
});
