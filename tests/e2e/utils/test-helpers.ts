import { Page, expect } from '@playwright/test';

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Login as admin user with mocked responses
   */
  async loginAsAdmin() {
    // Mock captcha generation
    await this.page.route('**/api/auth/generate-captcha/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          question: '2 + 2 = ?',
          answer: '4'
        })
      });
    });

    // Mock successful login
    await this.page.route('**/api/auth/login/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: '1',
            email: '<EMAIL>',
            full_name: 'Admin User',
            user_type: 'admin'
          },
          permissions: {
            can_access_admin: true,
            can_create_users: true,
            can_manage_groups: true
          }
        })
      });
    });

    await this.page.goto('/bce');
    await this.page.locator('input[placeholder*="email" i]').fill('<EMAIL>');
    await this.page.locator('input[placeholder*="password" i]').fill('admin123');
    await this.page.locator('input[placeholder*="captcha" i]').fill('4');
    await this.page.locator('button:has-text("Sign In")').click();
    
    await this.page.waitForURL('**/dashboard', { timeout: 10000 });
  }

  /**
   * Login as regular user with mocked responses
   */
  async loginAsUser() {
    await this.page.route('**/api/auth/generate-captcha/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          question: '2 + 2 = ?',
          answer: '4'
        })
      });
    });

    await this.page.route('**/api/auth/login/', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: '2',
            email: '<EMAIL>',
            full_name: 'Regular User',
            user_type: 'user'
          },
          permissions: {
            can_access_admin: false,
            can_create_users: false,
            can_manage_groups: false
          }
        })
      });
    });

    await this.page.goto('/bce');
    await this.page.locator('input[placeholder*="email" i]').fill('<EMAIL>');
    await this.page.locator('input[placeholder*="password" i]').fill('user123');
    await this.page.locator('input[placeholder*="captcha" i]').fill('4');
    await this.page.locator('button:has-text("Sign In")').click();
    
    await this.page.waitForURL('**/dashboard', { timeout: 10000 });
  }

  /**
   * Mock users API response
   */
  async mockUsersAPI(users: any[] = []) {
    const defaultUsers = [
      {
        id: '1',
        email: '<EMAIL>',
        full_name: 'Test User 1',
        user_type: 'user',
        is_active: true,
        can_deactivate: true,
        groups: []
      },
      {
        id: '2',
        email: '<EMAIL>',
        full_name: 'Test User 2',
        user_type: 'admin',
        is_active: false,
        can_deactivate: true,
        groups: []
      }
    ];

    await this.page.route('**/api/admin-controls/users/**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            users: users.length > 0 ? users : defaultUsers,
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: users.length || defaultUsers.length,
              has_next: false,
              has_previous: false
            }
          }
        })
      });
    });
  }

  /**
   * Mock groups API response
   */
  async mockGroupsAPI(groups: any[] = []) {
    const defaultGroups = [
      {
        id: 1,
        name: 'Administrators',
        permissions_count: 10,
        users_count: 2
      },
      {
        id: 2,
        name: 'Users',
        permissions_count: 3,
        users_count: 5
      }
    ];

    await this.page.route('**/api/admin-controls/groups/**', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              groups: groups.length > 0 ? groups : defaultGroups,
              pagination: {
                current_page: 1,
                total_pages: 1,
                total_count: groups.length || defaultGroups.length,
                has_next: false,
                has_previous: false
              }
            }
          })
        });
      } else {
        // Mock POST/PUT/DELETE responses
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });
  }

  /**
   * Navigate to admin panel
   */
  async navigateToAdminPanel() {
    await this.page.locator('text=Admin Panel').click();
    await expect(this.page.locator('text=User Management')).toBeVisible();
    await expect(this.page.locator('text=Groups & Permissions')).toBeVisible();
  }

  /**
   * Navigate to user management
   */
  async navigateToUserManagement() {
    await this.navigateToAdminPanel();
    await this.page.locator('text=User Management').click();
    await expect(this.page.locator('h2')).toContainText('User Management');
  }

  /**
   * Navigate to group management
   */
  async navigateToGroupManagement() {
    await this.navigateToAdminPanel();
    await this.page.locator('text=Groups & Permissions').click();
    await expect(this.page.locator('h2')).toContainText('Group Management');
  }

  /**
   * Fill and submit create user form
   */
  async createUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    userType?: string;
  }) {
    await this.page.locator('button:has-text("Add User")').click();
    
    await this.page.locator('input[placeholder*="email" i]').fill(userData.email);
    await this.page.locator('input[placeholder*="password" i]').fill(userData.password);
    await this.page.locator('input[placeholder*="first name" i]').fill(userData.firstName);
    await this.page.locator('input[placeholder*="last name" i]').fill(userData.lastName);
    
    if (userData.userType) {
      await this.page.locator('select[name="user_type"]').selectOption(userData.userType);
    }
    
    await this.page.locator('button:has-text("Create User")').click();
  }

  /**
   * Fill and submit create group form
   */
  async createGroup(groupName: string) {
    await this.page.locator('button:has-text("Add Group")').click();
    await this.page.locator('input[placeholder*="group name" i]').fill(groupName);
    await this.page.locator('button:has-text("Create Group")').click();
  }

  /**
   * Wait for element to be visible with timeout
   */
  async waitForElement(selector: string, timeout: number = 5000) {
    await this.page.waitForSelector(selector, { timeout });
  }

  /**
   * Take screenshot for debugging
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `test-results/screenshots/${name}.png` });
  }

  /**
   * Mock API error response
   */
  async mockAPIError(endpoint: string, status: number = 500, message: string = 'Server Error') {
    await this.page.route(endpoint, async route => {
      await route.fulfill({
        status,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: message
        })
      });
    });
  }
}
