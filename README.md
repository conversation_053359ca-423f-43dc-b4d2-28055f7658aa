# Talent Hero v3.11

A comprehensive talent management system built with Django REST Framework backend and React TypeScript frontend. Features role-based access control with superadmin/admin/user hierarchy, vendor management, and group-based permissions.

## Key Features

- **Multi-tier Authentication**: Vendor and BCE login paths with arithmetic captcha
- **User Management**: Create, edit, activate/deactivate users with bulk operations
- **Group Management**: Assign users to groups with permission controls
- **Admin Panel**: Comprehensive dashboard for system administration
- **Dark Mode UI**: Modern interface using shadcn components

## Tech Stack

**Backend**: Django 5.2.4, PostgreSQL, MongoDB, Django Guardian
**Frontend**: React, TypeScript, Vite, Tailwind CSS
**Authentication**: Session-based with CSRF protection

## Quick Start

Backend: `cd backend && python manage.py runserver`
Frontend: `npm run dev`

Access BCE panel at `/bce` for admin users, `/vendor` for vendor access.

## Testing

- **python test.py**: Main test runner for both frontend and backend
- **npm run test**: Frontend tests only
- **npm run test**:coverage: Frontend tests with coverage
- **python run_tests.py**: Comprehensive test suite with reporting

- **Run all tests (unit + integration + E2E)**
python test_windows.py

- **Run individual test types**
npm run test              # Frontend unit tests
npm run test:e2e          # E2E tests only
cd backend && python manage.py test  # Backend tests

- **View test reports**
open test-results/latest-test-report.html for a Comprehensive report
npx playwright show-report for a E2E test report

## Testing Commands Simplified

- **test_unit.py**: Unit tests only, fast execution, no server startup
- **test_all.py**: Everything (unit + integration + E2E), automatic server startup, comprehensive
- **test_e2e.py**: E2E only, requires manual server startup, UI testing focus