#!/usr/bin/env python3
"""
Comprehensive test runner for Talent Hero application
Runs both frontend and backend tests and generates reports
"""
import os
import sys
import subprocess
import json
import datetime
from pathlib import Path


class TestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results_dir = self.project_root / 'test-results'
        self.test_results_dir.mkdir(exist_ok=True)
        
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'frontend': {},
            'backend': {},
            'summary': {}
        }
    
    def run_frontend_tests(self):
        """Run frontend tests with Vitest"""
        print("🧪 Running Frontend Tests...")
        print("=" * 50)

        try:
            # Determine npm command based on OS
            npm_cmd = 'npm.cmd' if os.name == 'nt' else 'npm'

            # Run tests with JSON reporter
            result = subprocess.run([
                npm_cmd, 'run', 'test:run', '--',
                '--reporter=json',
                f'--outputFile={self.test_results_dir}/frontend-results.json'
            ],
            cwd=str(self.project_root),  # Convert Path to string for Windows compatibility
            capture_output=True,
            text=True,
            timeout=300,
            shell=True  # Use shell on Windows
            )
            
            # Also run with default reporter for console output
            subprocess.run([
                npm_cmd, 'run', 'test:run'
            ], cwd=str(self.project_root), timeout=300, shell=True)
            
            # Read results
            results_file = self.test_results_dir / 'frontend-results.json'
            if results_file.exists():
                with open(results_file, 'r') as f:
                    frontend_results = json.load(f)
                
                self.results['frontend'] = {
                    'status': 'passed' if result.returncode == 0 else 'failed',
                    'return_code': result.returncode,
                    'test_results': frontend_results,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            else:
                self.results['frontend'] = {
                    'status': 'error',
                    'return_code': result.returncode,
                    'error': 'Results file not found',
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            
            print(f"✅ Frontend tests completed with return code: {result.returncode}")
            
        except subprocess.TimeoutExpired:
            print("❌ Frontend tests timed out")
            self.results['frontend'] = {
                'status': 'timeout',
                'error': 'Tests timed out after 300 seconds'
            }
        except Exception as e:
            print(f"❌ Error running frontend tests: {e}")
            self.results['frontend'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def run_backend_tests(self):
        """Run Django backend tests"""
        print("\n🧪 Running Backend Tests...")
        print("=" * 50)
        
        try:
            # Change to backend directory
            backend_dir = self.project_root / 'backend'

            # Run Django tests with JSON output
            result = subprocess.run([
                sys.executable, 'manage.py', 'test',
                '--verbosity=2',
                '--keepdb',
                '--parallel'
            ],
            cwd=str(backend_dir),  # Convert Path to string for Windows compatibility
            capture_output=True,
            text=True,
            timeout=300,
            shell=True  # Use shell on Windows
            )
            
            self.results['backend'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            print(f"✅ Backend tests completed with return code: {result.returncode}")
            
        except subprocess.TimeoutExpired:
            print("❌ Backend tests timed out")
            self.results['backend'] = {
                'status': 'timeout',
                'error': 'Tests timed out after 300 seconds'
            }
        except Exception as e:
            print(f"❌ Error running backend tests: {e}")
            self.results['backend'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_summary(self):
        """Generate test summary"""
        frontend_status = self.results['frontend'].get('status', 'unknown')
        backend_status = self.results['backend'].get('status', 'unknown')
        
        overall_status = 'passed'
        if frontend_status in ['failed', 'error', 'timeout'] or backend_status in ['failed', 'error', 'timeout']:
            overall_status = 'failed'
        
        self.results['summary'] = {
            'overall_status': overall_status,
            'frontend_status': frontend_status,
            'backend_status': backend_status,
            'timestamp': self.results['timestamp']
        }
    
    def save_results(self):
        """Save test results to file"""
        results_file = self.test_results_dir / f'test-report-{self.timestamp}.json'
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Also save as latest
        latest_file = self.test_results_dir / 'latest-test-report.json'
        with open(latest_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Test results saved to: {results_file}")
        print(f"📊 Latest results: {latest_file}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        
        summary = self.results['summary']
        
        # Overall status
        status_emoji = "✅" if summary['overall_status'] == 'passed' else "❌"
        print(f"{status_emoji} Overall Status: {summary['overall_status'].upper()}")
        
        # Frontend results
        frontend_emoji = "✅" if summary['frontend_status'] == 'passed' else "❌"
        print(f"{frontend_emoji} Frontend Tests: {summary['frontend_status'].upper()}")
        
        # Backend results
        backend_emoji = "✅" if summary['backend_status'] == 'passed' else "❌"
        print(f"{backend_emoji} Backend Tests: {summary['backend_status'].upper()}")
        
        print(f"🕐 Timestamp: {summary['timestamp']}")
        
        # Detailed results
        if self.results['frontend'].get('test_results'):
            frontend_results = self.results['frontend']['test_results']
            if 'testResults' in frontend_results:
                total_tests = sum(len(test_file.get('assertionResults', [])) 
                                for test_file in frontend_results['testResults'])
                passed_tests = sum(len([test for test in test_file.get('assertionResults', []) 
                                      if test.get('status') == 'passed']) 
                                 for test_file in frontend_results['testResults'])
                print(f"📊 Frontend: {passed_tests}/{total_tests} tests passed")
        
        if 'failed' in summary['frontend_status'] or 'failed' in summary['backend_status']:
            print("\n❌ Some tests failed. Check the detailed results above.")
        
        print("=" * 60)
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Comprehensive Test Suite")
        print("=" * 60)
        
        # Run tests
        self.run_frontend_tests()
        self.run_backend_tests()
        
        # Generate summary and save results
        self.generate_summary()
        self.save_results()
        self.print_summary()
        
        # Return exit code based on overall status
        return 0 if self.results['summary']['overall_status'] == 'passed' else 1


def main():
    """Main entry point"""
    runner = TestRunner()
    exit_code = runner.run_all_tests()
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
