#!/usr/bin/env python3
"""
E2E tests only - Requires servers to be running manually
"""
import os
import sys
import subprocess
import json
import datetime
from pathlib import Path
import shutil


class E2ETestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results_dir = self.project_root / 'test-results'
        self.test_results_dir.mkdir(exist_ok=True)
        
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    
    def check_npm_available(self):
        """Check if npm is available"""
        npm_commands = ['npm', 'npm.cmd']
        for npm_cmd in npm_commands:
            if shutil.which(npm_cmd):
                return npm_cmd
        return None
    
    def check_servers_running(self):
        """Check if required servers are running"""
        import urllib.request
        import urllib.error
        
        try:
            # Check frontend server
            urllib.request.urlopen('http://localhost:5174', timeout=5)
            frontend_running = True
        except:
            frontend_running = False
        
        try:
            # Check backend server
            urllib.request.urlopen('http://localhost:8000', timeout=5)
            backend_running = True
        except:
            backend_running = False
        
        return frontend_running, backend_running
    
    def run_e2e_tests(self):
        """Run Playwright E2E tests"""
        print("🎭 E2E Test Suite (Playwright)")
        print("=" * 60)
        
        # Check if servers are running
        frontend_running, backend_running = self.check_servers_running()
        
        if not frontend_running:
            print("❌ Frontend server not running at http://localhost:5174")
            print("   Please start with: npm run dev")
            return 1
        
        if not backend_running:
            print("❌ Backend server not running at http://localhost:8000")
            print("   Please start with: cd backend && python manage.py runserver")
            return 1
        
        print("✅ Frontend server running at http://localhost:5174")
        print("✅ Backend server running at http://localhost:8000")
        
        npm_cmd = self.check_npm_available()
        if not npm_cmd:
            print("❌ npm not found. Please ensure Node.js and npm are installed.")
            return 1
        
        try:
            print("\n🎭 Running Playwright E2E Tests...")
            print("=" * 50)
            
            # Run Playwright tests without server startup
            result = subprocess.run([
                npm_cmd, 'run', 'test:e2e', '--', '--reporter=list'
            ], 
            cwd=str(self.project_root),
            text=True,
            timeout=300,  # 5 minutes
            shell=True,
            encoding='utf-8',
            errors='replace'
            )
            
            print(f"\n✅ E2E tests completed with return code: {result.returncode}")
            
            # Save results
            results = {
                'timestamp': datetime.datetime.now().isoformat(),
                'status': 'passed' if result.returncode == 0 else 'failed',
                'return_code': result.returncode,
            }
            
            results_file = self.test_results_dir / f'e2e-test-report-{self.timestamp}.json'
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"📊 E2E test results saved to: {results_file}")
            
            if result.returncode == 0:
                print("\n🎉 All E2E tests passed!")
            else:
                print("\n❌ Some E2E tests failed. Check the output above for details.")
            
            return result.returncode
            
        except subprocess.TimeoutExpired:
            print("❌ E2E tests timed out after 5 minutes")
            return 1
        except Exception as e:
            print(f"❌ Error running E2E tests: {e}")
            return 1


def main():
    runner = E2ETestRunner()
    exit_code = runner.run_e2e_tests()
    
    if exit_code == 0:
        print(f"\n🎉 E2E test execution completed successfully!")
    else:
        print(f"\n❌ E2E test execution failed!")
    
    print(f"📊 Check test-results/ directory for detailed reports")
    print(f"📊 Run 'npx playwright show-report' for visual E2E report")
    
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
